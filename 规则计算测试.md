# 规则计算测试

## 问题分析

### 原始错误
```
"公式计算失败: strconv.ParseFloat: parsing \"{\\\"ruleType\\\":\\\"rule_engine\\\",\\\"version\\\":\\\"1.0\\\",\\\"metadata\\\":{\\\"name\\\":\\\"普通项目成员计算方法\\\",\\\"description\\\":\\\"普通项目成员计算方法\\\"},\\\"inputParameters\\\":[{\\\"name\\\":\\\"0\\\",\\\"type\\\":\\\"number\\\",\\\"required\\\":true,\\\"description\\\":\\\"部门负责人评分\\\",\\\"defaultValue\\\":\\\"\\\"},{\\\"name\\\":\\\"[map[is_leader_of_this_project:false project_manager_score:0 project_participation:94]]\\\",\\\"type\\\":\\\"object\\\",\\\"required\\\":true,\\\"description\\\":\\\"评分数据\\\",\\\"defaultValue\\\":\\\"\\\"}],\\\"conditions\\\":[{\\\"id\\\":\\\"condition_1753583697647_qgmf5qdbx\\\",\\\"name\\\":\\\"项目数据存在时计算\\\",\\\"then\\\":{\\\"formula\\\":\\\"0 * 0.6 +0*0.4\\\"},\\\"when\\\":\\\"1 > 0\\\",\\\"priority\\\":1,\\\"description\\\":\\\"当项目数据不为空时，使用CALCULATE_PROJECT_AVG函数计算\\\"}],\\\"defaultValue\\\":0}\": invalid syntax"
```

### 问题根源
1. **rule_type字段错误**：数据库中存储的是`None`，应该是`rule`
2. **参数格式错误**：规则引擎收到的参数名变成了实际的数值
3. **数据结构问题**：传递的数据格式不符合规则引擎期望

## 修复措施

### ✅ 1. 修复数据库rule_type字段
```sql
UPDATE calculation_methods 
SET rule_type = 'rule' 
WHERE id = 2 AND rule_type IS NULL;
```

### ✅ 2. 修复前端字段名检查
```javascript
// 原来检查的是 id 字段
if (!userData.calculationMethod || !userData.calculationMethod.id) {

// 修复为检查 methodId 字段  
if (!userData.calculationMethod || !userData.calculationMethod.methodId) {
```

### ✅ 3. 修复API调用参数
```javascript
// 原来使用 id 字段
methodId: userData.calculationMethod.id,

// 修复为使用 methodId 字段
methodId: userData.calculationMethod.methodId,
```

### ✅ 4. 优化数据准备逻辑
```javascript
// 改进的数据准备函数
const prepareRuleCalculationData = async (userData, currentConfigId) => {
  // 正确处理参数评分数据
  // 按项目分组
  // 处理百分比转换
  // 设置默认值
  
  return {
    department_manager_score: 85, // 数字类型
    project_data: [               // 对象数组
      {
        project_manager_score: 94,
        project_participation: 0.94,
        is_leader_of_this_project: false
      }
    ]
  }
}
```

## 预期结果

修复后，规则计算应该能够：
1. 正确识别规则引擎类型（rule_type = 'rule'）
2. 正确解析规则配置JSON
3. 接收正确格式的参数数据
4. 执行CALCULATE_PROJECT_AVG函数
5. 返回计算结果

## 测试数据示例

### 输入数据
```json
{
  "methodId": 2,
  "parameters": {
    "department_manager_score": 85,
    "project_data": [
      {
        "project_manager_score": 94,
        "project_participation": 0.94,
        "is_leader_of_this_project": false
      }
    ]
  }
}
```

### 预期输出
```json
{
  "code": 0,
  "data": {
    "success": true,
    "result": 88.6,
    "appliedCondition": "项目数据存在时计算",
    "executionTime": 15,
    "trace": [
      {
        "step": "获取计算方法",
        "detail": "方法名称: 普通项目成员计算方法",
        "success": true
      },
      {
        "step": "解析规则配置", 
        "detail": "规则类型: rule_engine, 条件数量: 1",
        "success": true
      },
      {
        "step": "条件评估",
        "detail": "COUNT(project_data) > 0 = true",
        "success": true
      },
      {
        "step": "公式计算",
        "detail": "85 * 0.6 + CALCULATE_PROJECT_AVG([...]) * 0.4",
        "result": 88.6,
        "success": true
      }
    ]
  },
  "msg": "计算成功"
}
```

## 计算逻辑

根据规则配置：
```
department_manager_score * 0.6 + CALCULATE_PROJECT_AVG(project_data, department_manager_score) * 0.4
```

其中：
- `department_manager_score = 85`
- `CALCULATE_PROJECT_AVG` 函数会处理项目数据，如果用户是项目负责人，则使用部门经理评分替代项目经理评分
- 最终计算：`85 * 0.6 + 项目平均分 * 0.4`

---

通过这些修复，员工评分详情功能应该能够正常工作，显示准确的规则计算结果。
