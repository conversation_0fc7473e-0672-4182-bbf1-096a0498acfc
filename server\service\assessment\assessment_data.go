package assessment

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type AssessmentDataService struct{}

// GetAssessmentData 获取考核数据
func (s *AssessmentDataService) GetAssessmentData(ctx context.Context, req assessmentReq.GetAssessmentDataRequest) (result assessmentReq.AssessmentDataResponse, err error) {
	// 1. 首先获取未归档的考核配置
	configs, err := s.getAssessmentConfigs(ctx)
	if err != nil {
		return result, fmt.Errorf("获取考核配置失败: %v", err)
	}
	result.AssessmentConfigs = configs

	// 2. 如果没有未归档的考核配置，直接返回基础数据
	if len(configs) == 0 {
		// 只获取用户作为项目负责人的项目数据
		projects, err := s.getManagerProjects(ctx, req.UserName)
		if err != nil {
			return result, fmt.Errorf("获取项目负责人数据失败: %v", err)
		}
		result.ManagerProjects = projects
		return result, nil
	}

	// 3. 如果有考核配置，则并发获取相关数据
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	// 获取管理员数据（如果是管理员且有考核配置）
	if req.IsAdmin {
		wg.Add(1)
		go func() {
			defer wg.Done()
			adminData, err := s.getAdminDataWithConfigs(ctx, req.OrgId, configs)
			if err != nil {
				mu.Lock()
				errors = append(errors, fmt.Errorf("获取管理员数据失败: %v", err))
				mu.Unlock()
				return
			}
			mu.Lock()
			result.AdminData = adminData
			mu.Unlock()
		}()
	}

	// 获取考核系数分配数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		coeffData, err := s.getCoefficientData(ctx, req.UserName, req.OrgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("获取系数数据失败: %v", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		result.CoefficientData = coeffData
		mu.Unlock()
	}()

	// 获取用户作为项目负责人的项目
	wg.Add(1)
	go func() {
		defer wg.Done()
		projects, err := s.getManagerProjects(ctx, req.UserName)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("获取项目负责人数据失败: %v", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		result.ManagerProjects = projects
		mu.Unlock()
	}()

	// 获取计算参数映射信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		parameters, err := s.getCalculationParameters(ctx)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("获取计算参数失败: %v", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		result.CalculationParameters = parameters
		mu.Unlock()
	}()

	// 等待所有协程完成
	wg.Wait()

	// 检查是否有错误
	if len(errors) > 0 {
		return result, errors[0]
	}

	return result, nil
}

// GetUserParameterScores 获取用户参数评分数据（支持批量查询）
func (s *AssessmentDataService) GetUserParameterScores(ctx context.Context, req assessmentReq.GetUserParameterScoresRequest) (*assessmentReq.GetUserParameterScoresResponse, error) {
	// 步骤1: 批量获取用户信息和计算方法
	userInfoMap, calcMethodMap, err := s.batchGetUserInfoAndCalculationMethods(ctx, req.UserNames)
	if err != nil {
		return nil, fmt.Errorf("批量获取用户信息失败: %v", err)
	}

	// 步骤2: 确定查询的考核配置范围
	configIds, err := s.determineAssessmentConfigs(ctx, req.AssessmentConfigIds)
	if err != nil {
		return nil, fmt.Errorf("确定考核配置范围失败: %v", err)
	}

	// 步骤3: 收集需要校验的用户
	userValidationMap := make(map[string][]int)      // userName -> configIds
	userDeptManagerScoreMap := make(map[string]bool) // userName -> hasDepartmentManagerScore

	for _, userName := range req.UserNames {
		calcMethod := calcMethodMap[userName]
		if calcMethod != nil {
			// 检查用户的计算参数是否包含 project_participation 和 project_manager_score
			hasProjectParticipation := false
			hasProjectManagerScore := false
			hasDepartmentManagerScore := false

			for _, param := range calcMethod.AssignedParameters {
				switch param.ParameterName {
				case "project_participation":
					hasProjectParticipation = true
				case "project_manager_score":
					hasProjectManagerScore = true
				case "department_manager_score":
					hasDepartmentManagerScore = true
				}
			}

			// 如果用户的计算参数包含 project_participation 和 project_manager_score，记录需要校验的用户
			if hasProjectParticipation && hasProjectManagerScore {
				userValidationMap[userName] = configIds
				userDeptManagerScoreMap[userName] = hasDepartmentManagerScore
			}
		}
	}

	// 步骤4: 批量校验用户评分完整性
	validationResults := make(map[string][]assessmentReq.ValidationDetail)
	for userName, configIds := range userValidationMap {
		hasDeptManagerScore := userDeptManagerScoreMap[userName]
		var allValidationDetails []assessmentReq.ValidationDetail

		for _, configId := range configIds {
			validationDetails := s.validateUserScoreCompleteness(ctx, userName, configId, hasDeptManagerScore)
			allValidationDetails = append(allValidationDetails, validationDetails...)
		}

		validationResults[userName] = allValidationDetails
	}

	// 步骤5: 批量查询所有用户的参数评分数据
	allParameterScores, err := s.batchQueryParameterScoresFromAllTables(ctx, req.UserNames, configIds, req.ParameterNames)
	if err != nil {
		return nil, fmt.Errorf("批量查询参数评分失败: %v", err)
	}

	// 步骤6: 组装每个用户的返回结果
	users := make([]assessmentReq.UserParameterScores, 0, len(req.UserNames))
	totalRecords := 0

	for _, userName := range req.UserNames {
		// 获取该用户的校验结果
		userValidationDetails := validationResults[userName]
		if userValidationDetails == nil {
			userValidationDetails = []assessmentReq.ValidationDetail{}
		}

		userScores := s.assembleUserParameterScores(
			userName,
			userInfoMap[userName],
			calcMethodMap[userName],
			allParameterScores[userName],
			req.ParameterNames,
			userValidationDetails,
		)
		users = append(users, userScores)
		totalRecords += len(userScores.ParameterScores)
	}

	// 步骤5: 计算批量查询汇总信息
	summary := s.calculateBatchSummary(users, totalRecords)

	response := &assessmentReq.GetUserParameterScoresResponse{
		Users:   users,
		Summary: summary,
	}

	return response, nil
}

// batchGetUserInfoAndCalculationMethods 批量获取用户信息和计算方法
func (s *AssessmentDataService) batchGetUserInfoAndCalculationMethods(ctx context.Context, userNames []string) (map[string]*assessmentReq.UserInfo, map[string]*assessmentReq.CalculationMethod, error) {
	userInfoMap := make(map[string]*assessmentReq.UserInfo)
	calcMethodMap := make(map[string]*assessmentReq.CalculationMethod)

	// 批量获取用户基本信息，包含组织名称
	var users []struct {
		UserName         string `json:"userName"`
		NickName         string `json:"nickName"`
		UserId           int    `json:"userId"`
		OrganizationName string `json:"organizationName"`
	}

	err := global.GVA_DB.Table("sys_users u").
		Select("u.username as user_name, u.nick_name, u.id as user_id, COALESCE(o.name, '') as organization_name").
		Joins("LEFT JOIN org_organizational_user ou ON u.id = ou.user_id").
		Joins("LEFT JOIN org_org o ON ou.org_id = o.id").
		Where("u.username IN ?", userNames).
		Scan(&users).Error
	if err != nil {
		return nil, nil, err
	}

	// 构建用户信息映射
	for _, user := range users {
		userInfoMap[user.UserName] = &assessmentReq.UserInfo{
			UserName:         user.UserName,
			NickName:         user.NickName,
			UserId:           user.UserId,
			OrganizationName: user.OrganizationName,
		}
	}

	// 批量获取计算方法信息
	// 注意：method_user_assignments表中的user_name字段存储的是用户ID，不是用户名
	// 需要先将用户名转换为用户ID
	var userIds []string
	for _, user := range users {
		userIds = append(userIds, fmt.Sprintf("%d", user.UserId))
	}

	var methodAssignments []struct {
		UserName             string `json:"userName"`
		UserId               int    `json:"userId"`
		MethodId             int    `json:"methodId"`
		MethodName           string `json:"methodName"`
		Description          string `json:"description"`
		Formula              string `json:"formula"`
		AssessmentCategoryId int    `json:"assessmentCategoryId"`
	}

	if len(userIds) > 0 {
		err = global.GVA_DB.Table("method_user_assignments mua").
			Select("mua.user_name, su.id as user_id, cm.id as method_id, cm.method_name, cm.description, cm.formula, cm.assessment_category_id").
			Joins("LEFT JOIN calculation_methods cm ON mua.method_id = cm.id").
			Joins("LEFT JOIN sys_users su ON mua.user_name = CAST(su.id AS CHAR)").
			Where("mua.user_name IN ?", userIds).
			Scan(&methodAssignments).Error
		if err != nil {
			return nil, nil, err
		}
	}

	// 创建用户ID到用户名的映射
	userIdToNameMap := make(map[int]string)
	for _, user := range users {
		userIdToNameMap[user.UserId] = user.UserName
	}

	// 为每个用户获取计算方法和参数
	for _, assignment := range methodAssignments {
		// 获取方法关联的参数
		parameters, err := s.getMethodParameters(ctx, assignment.MethodId)
		if err != nil {
			return nil, nil, err
		}

		// 通过用户ID找到对应的用户名
		if userName, exists := userIdToNameMap[assignment.UserId]; exists {
			calcMethodMap[userName] = &assessmentReq.CalculationMethod{
				MethodId:             assignment.MethodId,
				MethodName:           assignment.MethodName,
				Description:          assignment.Description,
				Formula:              assignment.Formula,
				AssessmentCategoryId: assignment.AssessmentCategoryId,
				AssignedParameters:   parameters,
			}
		}
	}

	// 为没有计算方法的用户设置 nil
	for _, userName := range userNames {
		if _, exists := calcMethodMap[userName]; !exists {
			calcMethodMap[userName] = nil
		}
		if _, exists := userInfoMap[userName]; !exists {
			userInfoMap[userName] = &assessmentReq.UserInfo{
				UserName: userName,
				NickName: "未知用户",
				UserId:   0,
			}
		}
	}

	return userInfoMap, calcMethodMap, nil
}

// determineAssessmentConfigs 确定查询的考核配置范围
func (s *AssessmentDataService) determineAssessmentConfigs(ctx context.Context, requestedConfigIds []int) ([]int, error) {
	// 如果指定了考核配置ID，直接使用
	if len(requestedConfigIds) > 0 {
		return requestedConfigIds, nil
	}

	// 否则查询所有未归档的考核配置
	var configIds []int
	err := global.GVA_DB.Table("assessment_config").
		Select("id").
		Where("is_archived = ?", false).
		Pluck("id", &configIds).Error

	if err != nil {
		return nil, err
	}

	return configIds, nil
}

// batchQueryParameterScoresFromAllTables 批量查询三个表的参数评分数据
func (s *AssessmentDataService) batchQueryParameterScoresFromAllTables(ctx context.Context, userNames []string, configIds []int, parameterNames []string) (map[string][]assessmentReq.ParameterScoreDetail, error) {
	allUserScores := make(map[string][]assessmentReq.ParameterScoreDetail)

	// 初始化每个用户的评分列表
	for _, userName := range userNames {
		allUserScores[userName] = make([]assessmentReq.ParameterScoreDetail, 0)
	}

	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	// 并发查询三个表
	wg.Add(3)

	// 查询 assessment_coefficient_allocation 表
	go func() {
		defer wg.Done()
		scores, err := s.batchQueryFromCoefficientAllocation(ctx, userNames, configIds, parameterNames)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		for userName, userScores := range scores {
			allUserScores[userName] = append(allUserScores[userName], userScores...)
		}
		mu.Unlock()
	}()

	// 查询 project_manager_score 表
	go func() {
		defer wg.Done()
		scores, err := s.batchQueryFromProjectManagerScore(ctx, userNames, configIds, parameterNames)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		for userName, userScores := range scores {
			allUserScores[userName] = append(allUserScores[userName], userScores...)
		}
		mu.Unlock()
	}()

	// 查询 department_manager_score 表
	go func() {
		defer wg.Done()
		scores, err := s.batchQueryFromDepartmentManagerScore(ctx, userNames, configIds, parameterNames)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		for userName, userScores := range scores {
			allUserScores[userName] = append(allUserScores[userName], userScores...)
		}
		mu.Unlock()
	}()

	wg.Wait()

	if len(errors) > 0 {
		return nil, errors[0]
	}

	return allUserScores, nil
}

// batchQueryFromCoefficientAllocation 批量查询考核系数分配表
func (s *AssessmentDataService) batchQueryFromCoefficientAllocation(ctx context.Context, userNames []string, configIds []int, parameterNames []string) (map[string][]assessmentReq.ParameterScoreDetail, error) {
	var rawScores []struct {
		UserName             string    `json:"user_name"`
		RecordId             int       `json:"record_id"`
		AssessmentConfigId   int       `json:"assessment_config_id"`
		AssessmentConfigName string    `json:"assessment_config_name"`
		ParameterName        string    `json:"parameter_name"`
		ParameterNameCn      string    `json:"parameter_name_cn"`
		ScoreValue           *float64  `json:"score_value"`
		ProjectId            *int      `json:"project_id"`
		ProjectName          *string   `json:"project_name"`
		CreatedAt            time.Time `json:"created_at"`
	}

	// 构建查询条件
	query := `
		SELECT
			aca.username as user_name,
			aca.id as record_id,
			aca.assessment_config_id,
			ac.assessment_name as assessment_config_name,
			aca.calculation_parameter as parameter_name,
			COALESCE(cp.parameter_name_cn, aca.calculation_parameter) as parameter_name_cn,
			aca.assessment_coefficient as score_value,
			aca.project_id,
			pi.name as project_name,
			aca.created_at
		FROM assessment_coefficient_allocation aca
		LEFT JOIN assessment_config ac ON aca.assessment_config_id = ac.id
		LEFT JOIN project_info pi ON aca.project_id = pi.id
		LEFT JOIN calculation_parameters cp ON aca.calculation_parameter = cp.parameter_name
		WHERE aca.username IN ?
		AND aca.assessment_config_id IN ?`

	args := []interface{}{userNames, configIds}

	// 如果指定了参数名称，添加参数过滤条件
	if len(parameterNames) > 0 {
		query += " AND aca.calculation_parameter IN ?"
		args = append(args, parameterNames)
	}

	query += " ORDER BY aca.username, aca.assessment_config_id, aca.project_id, aca.created_at DESC"

	err := global.GVA_DB.Raw(query, args...).Scan(&rawScores).Error
	if err != nil {
		return nil, err
	}

	// 按用户名分组
	result := make(map[string][]assessmentReq.ParameterScoreDetail)
	for _, userName := range userNames {
		result[userName] = make([]assessmentReq.ParameterScoreDetail, 0)
	}

	for _, raw := range rawScores {
		score := assessmentReq.ParameterScoreDetail{
			ParameterName:        raw.ParameterName,
			ParameterNameCn:      raw.ParameterNameCn,
			DataSource:           "assessment_coefficient_allocation",
			ScoreValue:           raw.ScoreValue,
			AssessmentConfigId:   raw.AssessmentConfigId,
			AssessmentConfigName: raw.AssessmentConfigName,
			ProjectId:            raw.ProjectId,
			ProjectName:          raw.ProjectName,
			ScorerUsername:       nil,
			ScorerNickName:       nil,
			CreatedAt:            raw.CreatedAt,
			RecordId:             raw.RecordId,
		}
		result[raw.UserName] = append(result[raw.UserName], score)
	}

	return result, nil
}

// batchQueryFromProjectManagerScore 批量查询项目经理评分表
func (s *AssessmentDataService) batchQueryFromProjectManagerScore(ctx context.Context, userNames []string, configIds []int, parameterNames []string) (map[string][]assessmentReq.ParameterScoreDetail, error) {
	var rawScores []struct {
		UserName             string    `json:"user_name"`
		RecordId             int       `json:"record_id"`
		AssessmentConfigId   int       `json:"assessment_config_id"`
		AssessmentConfigName string    `json:"assessment_config_name"`
		ParameterName        string    `json:"parameter_name"`
		ParameterNameCn      string    `json:"parameter_name_cn"`
		ScoreValue           *float64  `json:"score_value"`
		ProjectId            *int      `json:"project_id"`
		ProjectName          *string   `json:"project_name"`
		ScorerUsername       *string   `json:"scorer_username"`
		ScorerNickName       *string   `json:"scorer_nick_name"`
		CreatedAt            time.Time `json:"created_at"`
	}

	// 构建查询条件
	query := `
		SELECT
			aca.username as user_name,
			pms.id as record_id,
			aca.assessment_config_id,
			ac.assessment_name as assessment_config_name,
			pms.calculation_parameter as parameter_name,
			COALESCE(cp.parameter_name_cn, pms.calculation_parameter) as parameter_name_cn,
			pms.manager_score as score_value,
			aca.project_id,
			pi.name as project_name,
			pms.scorer_username,
			su.nick_name as scorer_nick_name,
			pms.created_at
		FROM project_manager_score pms
		LEFT JOIN assessment_coefficient_allocation aca ON pms.coefficient_allocation_id = aca.id
		LEFT JOIN assessment_config ac ON aca.assessment_config_id = ac.id
		LEFT JOIN project_info pi ON aca.project_id = pi.id
		LEFT JOIN calculation_parameters cp ON pms.calculation_parameter = cp.parameter_name
		LEFT JOIN sys_users su ON pms.scorer_username = su.username
		WHERE aca.username IN ?
		AND aca.assessment_config_id IN ?`

	args := []interface{}{userNames, configIds}

	// 如果指定了参数名称，添加参数过滤条件
	if len(parameterNames) > 0 {
		query += " AND pms.calculation_parameter IN ?"
		args = append(args, parameterNames)
	}

	query += " ORDER BY aca.username, aca.assessment_config_id, aca.project_id, pms.created_at DESC"

	err := global.GVA_DB.Raw(query, args...).Scan(&rawScores).Error
	if err != nil {
		return nil, err
	}

	// 按用户名分组
	result := make(map[string][]assessmentReq.ParameterScoreDetail)
	for _, userName := range userNames {
		result[userName] = make([]assessmentReq.ParameterScoreDetail, 0)
	}

	for _, raw := range rawScores {
		score := assessmentReq.ParameterScoreDetail{
			ParameterName:        raw.ParameterName,
			ParameterNameCn:      raw.ParameterNameCn,
			DataSource:           "project_manager_score",
			ScoreValue:           raw.ScoreValue,
			AssessmentConfigId:   raw.AssessmentConfigId,
			AssessmentConfigName: raw.AssessmentConfigName,
			ProjectId:            raw.ProjectId,
			ProjectName:          raw.ProjectName,
			ScorerUsername:       raw.ScorerUsername,
			ScorerNickName:       raw.ScorerNickName,
			CreatedAt:            raw.CreatedAt,
			RecordId:             raw.RecordId,
		}
		result[raw.UserName] = append(result[raw.UserName], score)
	}

	return result, nil
}

// batchQueryFromDepartmentManagerScore 批量查询部门经理评分表
func (s *AssessmentDataService) batchQueryFromDepartmentManagerScore(ctx context.Context, userNames []string, configIds []int, parameterNames []string) (map[string][]assessmentReq.ParameterScoreDetail, error) {
	var rawScores []struct {
		UserName             string    `json:"user_name"`
		RecordId             int       `json:"record_id"`
		AssessmentConfigId   int       `json:"assessment_config_id"`
		AssessmentConfigName string    `json:"assessment_config_name"`
		ParameterName        string    `json:"parameter_name"`
		ParameterNameCn      string    `json:"parameter_name_cn"`
		ScoreValue           *float64  `json:"score_value"`
		ScorerUsername       *string   `json:"scorer_username"`
		ScorerNickName       *string   `json:"scorer_nick_name"`
		CreatedAt            time.Time `json:"created_at"`
	}

	// 构建查询条件
	query := `
		SELECT
			dms.username as user_name,
			dms.id as record_id,
			dms.assessment_config_id,
			ac.assessment_name as assessment_config_name,
			dms.calculation_parameter as parameter_name,
			COALESCE(cp.parameter_name_cn, dms.calculation_parameter) as parameter_name_cn,
			dms.manager_score as score_value,
			dms.scorer_username,
			su.nick_name as scorer_nick_name,
			dms.created_at
		FROM department_manager_score dms
		LEFT JOIN assessment_config ac ON dms.assessment_config_id = ac.id
		LEFT JOIN calculation_parameters cp ON dms.calculation_parameter = cp.parameter_name
		LEFT JOIN sys_users su ON dms.scorer_username = su.username
		WHERE dms.username IN ?
		AND dms.assessment_config_id IN ?`

	args := []interface{}{userNames, configIds}

	// 如果指定了参数名称，添加参数过滤条件
	if len(parameterNames) > 0 {
		query += " AND dms.calculation_parameter IN ?"
		args = append(args, parameterNames)
	}

	query += " ORDER BY dms.username, dms.assessment_config_id, dms.created_at DESC"

	err := global.GVA_DB.Raw(query, args...).Scan(&rawScores).Error
	if err != nil {
		return nil, err
	}

	// 按用户名分组
	result := make(map[string][]assessmentReq.ParameterScoreDetail)
	for _, userName := range userNames {
		result[userName] = make([]assessmentReq.ParameterScoreDetail, 0)
	}

	for _, raw := range rawScores {
		score := assessmentReq.ParameterScoreDetail{
			ParameterName:        raw.ParameterName,
			ParameterNameCn:      raw.ParameterNameCn,
			DataSource:           "department_manager_score",
			ScoreValue:           raw.ScoreValue,
			AssessmentConfigId:   raw.AssessmentConfigId,
			AssessmentConfigName: raw.AssessmentConfigName,
			ProjectId:            nil, // 部门评分没有项目关联
			ProjectName:          nil,
			ScorerUsername:       raw.ScorerUsername,
			ScorerNickName:       raw.ScorerNickName,
			CreatedAt:            raw.CreatedAt,
			RecordId:             raw.RecordId,
		}
		result[raw.UserName] = append(result[raw.UserName], score)
	}

	return result, nil
}

// getAssessmentConfigs 获取未归档的考核配置
func (s *AssessmentDataService) getAssessmentConfigs(ctx context.Context) ([]assessmentReq.AssessmentConfigInfo, error) {
	var configs []assessmentReq.AssessmentConfigInfo

	err := global.GVA_DB.Table("assessment_config").
		Select("id, assessment_name, assessment_type, assessment_period, is_archived, algorithm_relation_id, bonus_relation_id, score_quota_id, created_at").
		Where("is_archived = ?", false).
		Scan(&configs).Error

	return configs, err
}

// getAdminDataWithConfigs 根据考核配置获取管理员数据
func (s *AssessmentDataService) getAdminDataWithConfigs(ctx context.Context, orgId int, configs []assessmentReq.AssessmentConfigInfo) (*assessmentReq.AdminData, error) {
	adminData := &assessmentReq.AdminData{}
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	// 从考核配置中提取奖金和配额ID
	bonusIds := make(map[int]bool)
	quotaIds := make(map[int]bool)

	for _, config := range configs {
		if config.BonusRelationId > 0 {
			bonusIds[config.BonusRelationId] = true
		}
		if config.ScoreQuotaId > 0 {
			quotaIds[config.ScoreQuotaId] = true
		}
	}

	// 并发获取奖金、配额、组织成员、部门负责人评分信息
	wg.Add(4)

	// 获取奖金信息（基于考核配置中的关联ID，按考核配置分组）
	go func() {
		defer wg.Done()
		bonusInfoMap, err := s.getBonusInfoByConfigs(ctx, configs, orgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.BonusInfo = bonusInfoMap
		mu.Unlock()
	}()

	// 获取配额信息（基于考核配置中的关联ID，只返回当前组织的信息）
	go func() {
		defer wg.Done()
		quotaInfo, err := s.getQuotaInfoByIds(ctx, quotaIds, orgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.QuotaInfo = quotaInfo
		mu.Unlock()
	}()

	// 获取组织成员信息
	go func() {
		defer wg.Done()
		orgMembers, err := s.getOrgMembers(ctx, orgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.OrgMembers = orgMembers
		mu.Unlock()
	}()

	// 获取部门负责人评分信息
	go func() {
		defer wg.Done()
		departmentScores, err := s.getDepartmentManagerScores(ctx, configs, orgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.DepartmentManagerScores = departmentScores
		mu.Unlock()
	}()

	wg.Wait()

	if len(errors) > 0 {
		return nil, errors[0]
	}

	return adminData, nil
}

// getBonusInfoByIds 根据ID集合获取奖金信息，只返回指定组织的信息
func (s *AssessmentDataService) getBonusInfoByIds(ctx context.Context, bonusIds map[int]bool, orgId int) (assessmentReq.BonusInfo, error) {
	var bonusInfo assessmentReq.BonusInfo

	// 如果没有奖金ID，返回空数据
	if len(bonusIds) == 0 {
		return bonusInfo, nil
	}

	// 将map转换为切片
	ids := make([]int, 0, len(bonusIds))
	for id := range bonusIds {
		ids = append(ids, id)
	}

	// 获取最新的奖金记录（从指定ID中选择）
	var bonus assessment.BonusManagement
	err := global.GVA_DB.Where("id IN ?", ids).Order("created_at DESC").First(&bonus).Error
	if err != nil {
		return bonusInfo, err
	}

	bonusInfo.Id = *bonus.Id
	bonusInfo.BonusName = *bonus.BonusName
	bonusInfo.Description = *bonus.Description

	// 解析部门分配JSON
	var allAllocations []assessmentReq.DepartmentAllocation
	if bonus.DepartmentAllocations != nil {
		err = json.Unmarshal(bonus.DepartmentAllocations, &allAllocations)
		if err != nil {
			return bonusInfo, err
		}
	}

	// 只返回当前组织ID对应的分配信息
	var filteredAllocations []assessmentReq.DepartmentAllocation
	var totalAmount float64
	for _, alloc := range allAllocations {
		if alloc.DepartmentId == orgId {
			filteredAllocations = append(filteredAllocations, alloc)
			totalAmount += alloc.AllocatedAmount
		}
	}

	bonusInfo.DepartmentAllocations = filteredAllocations
	bonusInfo.TotalAmount = totalAmount

	return bonusInfo, nil
}

// getBonusInfoByConfigs 根据考核配置获取奖金信息，按考核配置ID分组
func (s *AssessmentDataService) getBonusInfoByConfigs(ctx context.Context, configs []assessmentReq.AssessmentConfigInfo, orgId int) (map[string]assessmentReq.BonusInfo, error) {
	bonusInfoMap := make(map[string]assessmentReq.BonusInfo)

	for _, config := range configs {
		configKey := fmt.Sprintf("%d", config.Id)

		if config.BonusRelationId > 0 {
			// 查询奖金信息
			var bonus assessment.BonusManagement
			err := global.GVA_DB.Where("id = ?", config.BonusRelationId).First(&bonus).Error
			if err != nil {
				// 如果找不到奖金记录，跳过这个配置
				continue
			}

			bonusInfo := assessmentReq.BonusInfo{
				Id:          *bonus.Id,
				BonusName:   *bonus.BonusName,
				Description: *bonus.Description,
			}

			// 解析部门分配JSON
			var allAllocations []assessmentReq.DepartmentAllocation
			if bonus.DepartmentAllocations != nil {
				err = json.Unmarshal(bonus.DepartmentAllocations, &allAllocations)
				if err != nil {
					continue
				}
			}

			// 只返回当前组织ID对应的分配信息
			var filteredAllocations []assessmentReq.DepartmentAllocation
			var totalAmount float64
			for _, alloc := range allAllocations {
				if alloc.DepartmentId == orgId {
					filteredAllocations = append(filteredAllocations, alloc)
					totalAmount += alloc.AllocatedAmount
				}
			}

			bonusInfo.DepartmentAllocations = filteredAllocations
			bonusInfo.TotalAmount = totalAmount

			bonusInfoMap[configKey] = bonusInfo
		}
	}

	return bonusInfoMap, nil
}

// getQuotaInfoByIds 根据ID集合获取配额信息，只返回指定组织的信息
func (s *AssessmentDataService) getQuotaInfoByIds(ctx context.Context, quotaIds map[int]bool, orgId int) (assessmentReq.QuotaInfo, error) {
	var quotaInfo assessmentReq.QuotaInfo

	// 如果没有配额ID，返回空数据
	if len(quotaIds) == 0 {
		return quotaInfo, nil
	}

	// 将map转换为切片
	ids := make([]int, 0, len(quotaIds))
	for id := range quotaIds {
		ids = append(ids, id)
	}

	// 获取最新的配额记录（从指定ID中选择）
	var quota assessment.ScoreQuotaManagement
	err := global.GVA_DB.Where("id IN ?", ids).Order("created_at DESC").First(&quota).Error
	if err != nil {
		return quotaInfo, err
	}

	quotaInfo.Id = *quota.Id
	quotaInfo.QuotaName = *quota.QuotaName
	quotaInfo.Description = *quota.Description

	// 解析部门配额JSON
	var allQuotas []assessmentReq.DepartmentQuota
	if quota.DepartmentQuotas != nil {
		err = json.Unmarshal(quota.DepartmentQuotas, &allQuotas)
		if err != nil {
			return quotaInfo, err
		}
	}

	// 只返回当前组织ID对应的配额信息
	var filteredQuotas []assessmentReq.DepartmentQuota
	for _, q := range allQuotas {
		if q.DepartmentId == orgId {
			filteredQuotas = append(filteredQuotas, q)
		}
	}

	quotaInfo.DepartmentQuotas = filteredQuotas

	return quotaInfo, nil
}

// getAdminData 获取管理员数据（保留原方法以兼容）
func (s *AssessmentDataService) getAdminData(ctx context.Context, orgId int) (*assessmentReq.AdminData, error) {
	adminData := &assessmentReq.AdminData{}
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	// 并发获取奖金、配额、组织成员信息
	wg.Add(3)

	// 获取奖金信息（兼容性方法，使用默认配置）
	go func() {
		defer wg.Done()
		bonusInfo, err := s.getBonusInfo(ctx)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		// 为兼容性，将单个奖金信息放入map中，使用"default"作为key
		adminData.BonusInfo = map[string]assessmentReq.BonusInfo{
			"default": bonusInfo,
		}
		mu.Unlock()
	}()

	// 获取配额信息
	go func() {
		defer wg.Done()
		quotaInfo, err := s.getQuotaInfo(ctx)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.QuotaInfo = quotaInfo
		mu.Unlock()
	}()

	// 获取组织成员信息
	go func() {
		defer wg.Done()
		orgMembers, err := s.getOrgMembers(ctx, orgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.OrgMembers = orgMembers
		mu.Unlock()
	}()

	wg.Wait()

	if len(errors) > 0 {
		return nil, errors[0]
	}

	return adminData, nil
}

// getBonusInfo 获取奖金信息
func (s *AssessmentDataService) getBonusInfo(ctx context.Context) (assessmentReq.BonusInfo, error) {
	var bonusInfo assessmentReq.BonusInfo

	// 获取最新的奖金记录
	var bonus assessment.BonusManagement
	err := global.GVA_DB.Order("created_at DESC").First(&bonus).Error
	if err != nil {
		return bonusInfo, err
	}

	bonusInfo.Id = *bonus.Id
	bonusInfo.BonusName = *bonus.BonusName
	bonusInfo.Description = *bonus.Description

	// 解析部门分配JSON
	var allocations []assessmentReq.DepartmentAllocation
	if bonus.DepartmentAllocations != nil {
		err = json.Unmarshal(bonus.DepartmentAllocations, &allocations)
		if err != nil {
			return bonusInfo, err
		}
	}
	bonusInfo.DepartmentAllocations = allocations

	// 计算总金额
	var totalAmount float64
	for _, alloc := range allocations {
		totalAmount += alloc.AllocatedAmount
	}
	bonusInfo.TotalAmount = totalAmount

	return bonusInfo, nil
}

// getQuotaInfo 获取配额信息
func (s *AssessmentDataService) getQuotaInfo(ctx context.Context) (assessmentReq.QuotaInfo, error) {
	var quotaInfo assessmentReq.QuotaInfo

	// 获取最新的配额记录
	var quota assessment.ScoreQuotaManagement
	err := global.GVA_DB.Order("created_at DESC").First(&quota).Error
	if err != nil {
		return quotaInfo, err
	}

	quotaInfo.Id = *quota.Id
	quotaInfo.QuotaName = *quota.QuotaName
	quotaInfo.Description = *quota.Description

	// 解析部门配额JSON
	var quotas []assessmentReq.DepartmentQuota
	if quota.DepartmentQuotas != nil {
		err = json.Unmarshal(quota.DepartmentQuotas, &quotas)
		if err != nil {
			return quotaInfo, err
		}
	}
	quotaInfo.DepartmentQuotas = quotas

	return quotaInfo, nil
}

// getCoefficientData 获取考核系数分配数据 - 返回所有考核配置的系数数据
func (s *AssessmentDataService) getCoefficientData(ctx context.Context, userName string, orgId int) (assessmentReq.CoefficientData, error) {
	var coeffData assessmentReq.CoefficientData

	// 获取最新的考核配置ID
	var latestAssessmentId int
	err := global.GVA_DB.Table("assessment_config").
		Select("id").
		Where("is_archived = ?", false).
		Order("created_at DESC").
		Limit(1).
		Scan(&latestAssessmentId).Error

	if err != nil {
		return coeffData, err
	}

	coeffData.CurrentAssessmentId = latestAssessmentId

	// 获取当前部门的所有用户名
	var departmentUsernames []string
	err = global.GVA_DB.Table("org_organizational_user ou").
		Select("u.username").
		Joins("LEFT JOIN sys_users u ON ou.user_id = u.id").
		Where("ou.org_id = ? AND u.deleted_at IS NULL", orgId).
		Pluck("username", &departmentUsernames).Error

	if err != nil {
		return coeffData, err
	}

	// 获取所有未归档考核配置的ID列表
	var allAssessmentIds []int
	err = global.GVA_DB.Table("assessment_config").
		Select("id").
		Where("is_archived = ?", false).
		Order("created_at DESC").
		Pluck("id", &allAssessmentIds).Error

	if err != nil {
		return coeffData, err
	}

	// 查询考核系数数据：包括本部门成员 + 当前用户负责项目中的其他部门成员
	var allCoeffs []assessmentReq.CoefficientInfo
	if len(allAssessmentIds) > 0 {
		// 获取当前用户负责的项目ID列表
		var managerProjectIds []int
		err = global.GVA_DB.Table("project_info").
			Select("id").
			Where("manager_id = ?", userName).
			Pluck("id", &managerProjectIds).Error

		if err != nil {
			return coeffData, err
		}

		// 构建查询条件：本部门成员 OR 当前用户负责项目中的成员
		var whereConditions []string
		var whereArgs []interface{}

		// 条件1：本部门成员的考核系数
		if len(departmentUsernames) > 0 {
			whereConditions = append(whereConditions, "(aca.assessment_config_id IN ? AND aca.username IN ?)")
			whereArgs = append(whereArgs, allAssessmentIds, departmentUsernames)
		}

		// 条件2：当前用户负责项目中的考核系数（包括其他部门成员）
		if len(managerProjectIds) > 0 {
			whereConditions = append(whereConditions, "(aca.assessment_config_id IN ? AND aca.project_id IN ?)")
			whereArgs = append(whereArgs, allAssessmentIds, managerProjectIds)
		}

		// 如果有查询条件，执行查询
		if len(whereConditions) > 0 {
			whereClause := strings.Join(whereConditions, " OR ")

			err = global.GVA_DB.Table("assessment_coefficient_allocation aca").
				Select(`aca.id, aca.assessment_config_id, aca.username, su.nick_name,
						aca.project_id, aca.assessment_coefficient, pms.manager_score,
						pms.scorer_username, aca.calculation_parameter, aca.created_at,
						GROUP_CONCAT(DISTINCT cp.parameter_name) as user_calculation_parameters`).
				Joins("LEFT JOIN project_manager_score pms ON aca.id = pms.coefficient_allocation_id").
				Joins("LEFT JOIN sys_users su ON aca.username = su.username").
				Joins("LEFT JOIN method_user_assignments mua ON mua.user_name = CAST(su.id AS CHAR)").
				Joins("LEFT JOIN method_parameter_relations mpr ON mua.method_id = mpr.method_id").
				Joins("LEFT JOIN calculation_parameters cp ON mpr.parameter_id = cp.id").
				Where(whereClause, whereArgs...).
				Group("aca.id, aca.assessment_config_id, aca.username, su.nick_name, aca.project_id, aca.assessment_coefficient, pms.manager_score, pms.scorer_username, aca.calculation_parameter, aca.created_at").
				Order("aca.assessment_config_id DESC, aca.created_at DESC").
				Scan(&allCoeffs).Error

			if err != nil {
				return coeffData, err
			}
		}
	}

	// 分离当前考核配置和其他考核配置的数据
	var currentCoeffs []assessmentReq.CoefficientInfo
	var otherCoeffs []assessmentReq.CoefficientInfo

	for _, coeff := range allCoeffs {
		if coeff.AssessmentConfigId == latestAssessmentId {
			currentCoeffs = append(currentCoeffs, coeff)
		} else {
			otherCoeffs = append(otherCoeffs, coeff)
		}
	}

	// 设置当前数据
	if len(currentCoeffs) > 0 {
		coeffData.HasCurrentData = true
		coeffData.Coefficients = currentCoeffs
	} else {
		coeffData.HasCurrentData = false
		coeffData.Coefficients = nil
	}

	// 设置所有未归档考核配置的数据
	coeffData.AllCoefficients = allCoeffs

	// 设置其他考核配置的数据（作为扩展数据返回）
	if len(otherCoeffs) > 0 {
		// 找到最近的一个有数据的考核配置作为previous
		var prevId int
		for _, id := range allAssessmentIds {
			if id != latestAssessmentId {
				for _, coeff := range otherCoeffs {
					if coeff.AssessmentConfigId == id {
						prevId = id
						break
					}
				}
				if prevId != 0 {
					break
				}
			}
		}

		if prevId != 0 {
			var prevCoeffs []assessmentReq.CoefficientInfo
			for _, coeff := range otherCoeffs {
				if coeff.AssessmentConfigId == prevId {
					prevCoeffs = append(prevCoeffs, coeff)
				}
			}
			coeffData.PreviousAssessmentId = &prevId
			coeffData.PreviousCoefficients = prevCoeffs
		}
	}

	return coeffData, nil
}

// getPreviousCoefficients 获取上一次的系数数据 - 返回当前部门所有成员的历史数据
func (s *AssessmentDataService) getPreviousCoefficients(ctx context.Context, userName string, currentAssessmentId int, orgId int) ([]assessmentReq.CoefficientInfo, int, error) {
	var prevCoeffs []assessmentReq.CoefficientInfo
	var prevAssessmentId int

	// 获取上一个考核配置ID
	err := global.GVA_DB.Table("assessment_config").
		Select("id").
		Where("id < ?", currentAssessmentId).
		Order("id DESC").
		Limit(1).
		Scan(&prevAssessmentId).Error

	if err != nil {
		return prevCoeffs, 0, err
	}

	// 获取当前部门的所有用户名
	var departmentUsernames []string
	err = global.GVA_DB.Table("org_organizational_user ou").
		Select("u.username").
		Joins("LEFT JOIN sys_users u ON ou.user_id = u.id").
		Where("ou.org_id = ? AND u.deleted_at IS NULL", orgId).
		Pluck("username", &departmentUsernames).Error

	if err != nil {
		return prevCoeffs, 0, err
	}

	// 获取上一次的系数数据 - 从新的两个表联合查询，查询部门所有成员
	if len(departmentUsernames) > 0 {
		err = global.GVA_DB.Table("assessment_coefficient_allocation aca").
			Select(`aca.id, aca.assessment_config_id, aca.username, su.nick_name,
					aca.project_id, aca.assessment_coefficient, pms.manager_score,
					pms.scorer_username, aca.calculation_parameter, aca.created_at`).
			Joins("LEFT JOIN project_manager_score pms ON aca.id = pms.coefficient_allocation_id").
			Joins("LEFT JOIN sys_users su ON aca.username = su.username").
			Where("aca.assessment_config_id = ? AND aca.username IN ?", prevAssessmentId, departmentUsernames).
			Scan(&prevCoeffs).Error
	}

	return prevCoeffs, prevAssessmentId, err
}

// getOrgMembers 获取组织成员信息
func (s *AssessmentDataService) getOrgMembers(ctx context.Context, orgId int) ([]assessmentReq.OrgMember, error) {
	// 获取组织成员基本信息，包含组织名称和代理负责人状态
	var members []struct {
		UserId           int    `json:"userId"`
		UserName         string `json:"userName"`
		NickName         string `json:"nickName"`
		Email            string `json:"email"`
		Phone            string `json:"phone"`
		AuthorityId      int    `json:"authorityId"`
		IsAdmin          bool   `json:"isAdmin"`
		IsAgentManager   bool   `json:"isAgentManager"`
		OrganizationName string `json:"organizationName"`
	}

	err := global.GVA_DB.Table("org_organizational_user ou").
		Select("u.id as user_id, u.username as user_name, u.nick_name, u.email, u.phone, ou.authority_id, ou.is_admin, COALESCE(ou.is_agent_manager, false) as is_agent_manager, o.name as organization_name").
		Joins("LEFT JOIN sys_users u ON ou.user_id = u.id").
		Joins("LEFT JOIN org_org o ON ou.org_id = o.id").
		Where("ou.org_id = ? AND u.deleted_at IS NULL", orgId).
		Scan(&members).Error

	if err != nil {
		return nil, err
	}

	// 并发获取每个成员的详细信息
	orgMembers := make([]assessmentReq.OrgMember, len(members))
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	for i, member := range members {
		wg.Add(1)
		go func(index int, m struct {
			UserId           int    `json:"userId"`
			UserName         string `json:"userName"`
			NickName         string `json:"nickName"`
			Email            string `json:"email"`
			Phone            string `json:"phone"`
			AuthorityId      int    `json:"authorityId"`
			IsAdmin          bool   `json:"isAdmin"`
			IsAgentManager   bool   `json:"isAgentManager"`
			OrganizationName string `json:"organizationName"`
		}) {
			defer wg.Done()

			orgMember := assessmentReq.OrgMember{
				UserId:           m.UserId,
				UserName:         m.UserName,
				NickName:         m.NickName,
				Email:            m.Email,
				Phone:            m.Phone,
				AuthorityId:      m.AuthorityId,
				IsAdmin:          m.IsAdmin,
				IsAgentManager:   m.IsAgentManager,
				OrganizationName: m.OrganizationName,
			}

			// 获取计算方法信息 - 传递用户ID而不是用户名
			calcMethod, err := s.getUserCalculationMethod(ctx, fmt.Sprintf("%d", m.UserId))
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}
			orgMember.CalculationMethod = calcMethod

			// 获取项目信息
			projects, err := s.getUserProjects(ctx, m.UserName)
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}
			orgMember.Projects = projects

			mu.Lock()
			orgMembers[index] = orgMember
			mu.Unlock()
		}(i, member)
	}

	wg.Wait()

	if len(errors) > 0 {
		return nil, errors[0]
	}

	return orgMembers, nil
}

// getUserCalculationMethod 获取用户的计算方法
func (s *AssessmentDataService) getUserCalculationMethod(ctx context.Context, userName string) (assessmentReq.CalculationMethod, error) {
	var calcMethod assessmentReq.CalculationMethod

	// 获取用户分配的计算方法
	var methodInfo struct {
		MethodId             int    `json:"methodId"`
		MethodName           string `json:"methodName"`
		Description          string `json:"description"`
		Formula              string `json:"formula"`
		AssessmentCategoryId int    `json:"assessmentCategoryId"`
	}

	err := global.GVA_DB.Table("method_user_assignments mua").
		Select("cm.id as method_id, cm.method_name, cm.description, cm.formula, cm.assessment_category_id").
		Joins("LEFT JOIN calculation_methods cm ON mua.method_id = cm.id").
		Where("mua.user_name = ?", userName).
		Scan(&methodInfo).Error

	if err != nil {
		return calcMethod, err
	}

	calcMethod.MethodId = methodInfo.MethodId
	calcMethod.MethodName = methodInfo.MethodName
	calcMethod.Description = methodInfo.Description
	calcMethod.Formula = methodInfo.Formula
	calcMethod.AssessmentCategoryId = methodInfo.AssessmentCategoryId

	// 获取方法关联的参数
	parameters, err := s.getMethodParameters(ctx, methodInfo.MethodId)
	if err != nil {
		return calcMethod, err
	}
	calcMethod.AssignedParameters = parameters

	return calcMethod, nil
}

// getMethodParameters 获取方法关联的参数
func (s *AssessmentDataService) getMethodParameters(ctx context.Context, methodId int) ([]assessmentReq.AssignedParameter, error) {
	var parameters []assessmentReq.AssignedParameter

	// 由于role_id现在是字符串类型（可能包含多个ID），我们不能直接JOIN
	// 先获取基本参数信息，角色名称在后续处理
	err := global.GVA_DB.Table("method_parameter_relations mpr").
		Select("cp.id, cp.parameter_name, cp.parameter_name_cn, cp.role_id, '' as role_name, cp.assessment_category_id").
		Joins("LEFT JOIN calculation_parameters cp ON mpr.parameter_id = cp.id").
		Where("mpr.method_id = ?", methodId).
		Scan(&parameters).Error

	return parameters, err
}

// getUserProjects 获取用户的项目信息
func (s *AssessmentDataService) getUserProjects(ctx context.Context, userName string) (assessmentReq.ProjectInfo, error) {
	var projects assessmentReq.ProjectInfo

	// 并发获取作为负责人和成员的项目
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	wg.Add(2)

	// 获取作为负责人的项目
	go func() {
		defer wg.Done()
		managerProjects, err := s.getProjectsAsManager(ctx, userName)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		projects.AsManager = managerProjects
		mu.Unlock()
	}()

	// 获取作为成员的项目
	go func() {
		defer wg.Done()
		memberProjects, err := s.getProjectsAsMember(ctx, userName)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		projects.AsMember = memberProjects
		mu.Unlock()
	}()

	wg.Wait()

	if len(errors) > 0 {
		return projects, errors[0]
	}

	return projects, nil
}

// getProjectsAsManager 获取作为负责人的项目
func (s *AssessmentDataService) getProjectsAsManager(ctx context.Context, userName string) ([]assessmentReq.ProjectDetail, error) {
	var projectsDB []assessmentReq.ProjectDetailDB

	err := global.GVA_DB.Table("project_info pi").
		Select("pi.id as project_id, pi.name as project_name, pi.department_id, org.name as department_name, pi.type, pi.members as members_json").
		Joins("LEFT JOIN org_org org ON pi.department_id = org.id").
		Where("pi.manager_id = ?", userName).
		Scan(&projectsDB).Error

	if err != nil {
		return nil, err
	}

	// 转换为最终结构体
	projects := make([]assessmentReq.ProjectDetail, len(projectsDB))
	for i, projectDB := range projectsDB {
		projects[i] = assessmentReq.ProjectDetail{
			ProjectId:      projectDB.ProjectId,
			ProjectName:    projectDB.ProjectName,
			DepartmentId:   projectDB.DepartmentId,
			DepartmentName: projectDB.DepartmentName,
			Type:           projectDB.Type,
		}

		// 解析JSON成员列表
		members, err := s.parseJSONMembers(projectDB.MembersJSON)
		if err != nil {
			return nil, err
		}
		projects[i].Members = members

		// 获取成员名称
		memberNames, err := s.getMemberNames(ctx, members)
		if err != nil {
			return nil, err
		}
		projects[i].MemberNames = memberNames
	}

	return projects, nil
}

// parseJSONMembers 解析JSON格式的成员列表
func (s *AssessmentDataService) parseJSONMembers(membersJSON string) ([]string, error) {
	if membersJSON == "" || membersJSON == "null" {
		return []string{}, nil
	}

	var members []string
	err := json.Unmarshal([]byte(membersJSON), &members)
	if err != nil {
		return []string{}, err
	}

	return members, nil
}

// getProjectsAsMember 获取作为成员的项目
func (s *AssessmentDataService) getProjectsAsMember(ctx context.Context, userName string) ([]assessmentReq.ProjectDetail, error) {
	var projectsDB []assessmentReq.ProjectDetailDB

	// 查询包含该用户的项目
	err := global.GVA_DB.Table("project_info pi").
		Select("pi.id as project_id, pi.name as project_name, pi.department_id, org.name as department_name, pi.manager_id, pi.type, pi.members as members_json").
		Joins("LEFT JOIN org_org org ON pi.department_id = org.id").
		Where("JSON_CONTAINS(pi.members, ?)", fmt.Sprintf(`"%s"`, userName)).
		Scan(&projectsDB).Error

	if err != nil {
		return nil, err
	}

	// 转换为最终结构体
	projects := make([]assessmentReq.ProjectDetail, len(projectsDB))
	for i, projectDB := range projectsDB {
		projects[i] = assessmentReq.ProjectDetail{
			ProjectId:      projectDB.ProjectId,
			ProjectName:    projectDB.ProjectName,
			DepartmentId:   projectDB.DepartmentId,
			DepartmentName: projectDB.DepartmentName,
			ManagerId:      projectDB.ManagerId,
			Type:           projectDB.Type,
		}

		// 获取项目负责人姓名
		managerName, err := s.getUserNickName(ctx, projectDB.ManagerId)
		if err != nil {
			return nil, err
		}
		projects[i].ManagerName = managerName

		// 解析JSON成员列表
		members, err := s.parseJSONMembers(projectDB.MembersJSON)
		if err != nil {
			return nil, err
		}
		projects[i].Members = members

		// 获取成员名称
		memberNames, err := s.getMemberNames(ctx, members)
		if err != nil {
			return nil, err
		}
		projects[i].MemberNames = memberNames
	}

	return projects, nil
}

// getMemberNames 获取成员姓名列表
func (s *AssessmentDataService) getMemberNames(ctx context.Context, members []string) ([]string, error) {
	if len(members) == 0 {
		return []string{}, nil
	}

	// 查询用户信息，保持顺序
	var users []struct {
		Username string `json:"username"`
		NickName string `json:"nickName"`
	}

	// 使用ORDER BY FIELD确保返回结果按照members的顺序
	orderClause := "FIELD(username"
	for _, member := range members {
		orderClause += ", '" + member + "'"
	}
	orderClause += ")"

	err := global.GVA_DB.Table("sys_users").
		Select("username, nick_name").
		Where("username IN ?", members).
		Order(orderClause).
		Scan(&users).Error

	if err != nil {
		return nil, err
	}

	// 按顺序提取昵称
	names := make([]string, len(users))
	for i, user := range users {
		names[i] = user.NickName
	}

	return names, nil
}

// getUserNickName 获取用户昵称
func (s *AssessmentDataService) getUserNickName(ctx context.Context, userName string) (string, error) {
	var nickName string
	err := global.GVA_DB.Table("sys_users").
		Select("nick_name").
		Where("username = ?", userName).
		Scan(&nickName).Error

	return nickName, err
}

// getManagerProjects 获取用户作为项目负责人的项目
func (s *AssessmentDataService) getManagerProjects(ctx context.Context, userName string) ([]assessmentReq.ManagerProjectInfo, error) {
	var projectsDB []assessmentReq.ManagerProjectInfoDB

	// 获取项目基本信息
	err := global.GVA_DB.Table("project_info pi").
		Select("pi.id as project_id, pi.name as project_name, pi.department_id, org.name as department_name, pi.type, pi.members as members_json").
		Joins("LEFT JOIN org_org org ON pi.department_id = org.id").
		Where("pi.manager_id = ?", userName).
		Scan(&projectsDB).Error

	if err != nil {
		return nil, err
	}

	// 转换为最终结构体
	projects := make([]assessmentReq.ManagerProjectInfo, len(projectsDB))

	// 并发获取每个项目的系数信息和成员名称
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	for i, projectDB := range projectsDB {
		wg.Add(1)
		go func(index int, pDB assessmentReq.ManagerProjectInfoDB) {
			defer wg.Done()

			// 基本信息赋值
			projects[index] = assessmentReq.ManagerProjectInfo{
				ProjectId:      pDB.ProjectId,
				ProjectName:    pDB.ProjectName,
				DepartmentId:   pDB.DepartmentId,
				DepartmentName: pDB.DepartmentName,
				Type:           pDB.Type,
			}

			// 解析JSON成员列表
			members, err := s.parseJSONMembers(pDB.MembersJSON)
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}

			// 获取成员名称
			memberNames, err := s.getMemberNames(ctx, members)
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}

			// 获取项目的系数信息
			coefficients, err := s.getProjectCoefficients(ctx, pDB.ProjectId)
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}

			mu.Lock()
			projects[index].Members = members
			projects[index].MemberNames = memberNames
			projects[index].Coefficients = coefficients
			mu.Unlock()
		}(i, projectDB)
	}

	wg.Wait()

	if len(errors) > 0 {
		return nil, errors[0]
	}

	return projects, nil
}

// getProjectCoefficients 获取项目的系数信息
func (s *AssessmentDataService) getProjectCoefficients(ctx context.Context, projectId int) ([]assessmentReq.CoefficientInfo, error) {
	var coefficients []assessmentReq.CoefficientInfo

	// 从新的两个表联合查询项目的系数信息
	err := global.GVA_DB.Table("assessment_coefficient_allocation aca").
		Select(`aca.id, aca.assessment_config_id, aca.username, su.nick_name,
				aca.project_id, aca.assessment_coefficient, pms.manager_score,
				pms.scorer_username, aca.calculation_parameter, aca.created_at`).
		Joins("LEFT JOIN project_manager_score pms ON aca.id = pms.coefficient_allocation_id").
		Joins("LEFT JOIN sys_users su ON aca.username = su.username").
		Where("aca.project_id = ?", projectId).
		Scan(&coefficients).Error

	return coefficients, err
}

// getDepartmentManagerScores 获取部门负责人评分数据
func (s *AssessmentDataService) getDepartmentManagerScores(ctx context.Context, configs []assessmentReq.AssessmentConfigInfo, orgId int) ([]assessmentReq.DepartmentManagerScoreInfo, error) {
	var departmentScores []assessmentReq.DepartmentManagerScoreInfo

	// 如果没有考核配置，返回空数据
	if len(configs) == 0 {
		return departmentScores, nil
	}

	// 提取考核配置ID
	configIds := make([]int, len(configs))
	configMap := make(map[int]assessmentReq.AssessmentConfigInfo)
	for i, config := range configs {
		configIds[i] = config.Id
		configMap[config.Id] = config
	}

	// 查询部门负责人评分数据
	type DepartmentManagerScoreDB struct {
		Id                   int       `json:"id"`
		AssessmentConfigId   int       `json:"assessmentConfigId"`
		Username             string    `json:"username"`
		DepartmentId         int       `json:"departmentId"`
		ManagerScore         *float64  `json:"managerScore"`
		BonusAmount          *float64  `json:"bonusAmount"`
		ScorerUsername       *string   `json:"scorerUsername"`
		CalculationParameter *string   `json:"calculationParameter"`
		CreatedAt            time.Time `json:"createdAt"`
		UpdatedAt            time.Time `json:"updatedAt"`
	}

	var scoresDB []DepartmentManagerScoreDB
	err := global.GVA_DB.Table("department_manager_score").
		Select("id, assessment_config_id, username, department_id, manager_score, bonus_amount, scorer_username, calculation_parameter, created_at, updated_at").
		Where("assessment_config_id IN ? AND department_id = ?", configIds, orgId).
		Order("assessment_config_id DESC, created_at DESC").
		Scan(&scoresDB).Error

	if err != nil {
		return nil, fmt.Errorf("查询部门负责人评分数据失败: %v", err)
	}

	// 转换为响应结构体
	for _, scoreDB := range scoresDB {
		scoreInfo := assessmentReq.DepartmentManagerScoreInfo{
			Id:                   scoreDB.Id,
			AssessmentConfigId:   scoreDB.AssessmentConfigId,
			AssessmentConfigName: "", // 稍后填充
			Username:             scoreDB.Username,
			UserNickName:         "", // 稍后填充
			DepartmentId:         scoreDB.DepartmentId,
			DepartmentName:       "", // 稍后填充
			ManagerScore:         scoreDB.ManagerScore,
			BonusAmount:          scoreDB.BonusAmount,
			ScorerUsername:       scoreDB.ScorerUsername,
			ScorerNickName:       nil, // 稍后填充
			CalculationParameter: scoreDB.CalculationParameter,
			CreatedAt:            scoreDB.CreatedAt,
			UpdatedAt:            scoreDB.UpdatedAt,
		}

		// 添加考核配置名称
		if config, exists := configMap[scoreDB.AssessmentConfigId]; exists {
			scoreInfo.AssessmentConfigName = config.AssessmentName
		}

		// 获取用户昵称
		userNickName, err := s.getUserNickName(ctx, scoreDB.Username)
		if err == nil {
			scoreInfo.UserNickName = userNickName
		}

		// 获取评分人昵称
		if scoreDB.ScorerUsername != nil && *scoreDB.ScorerUsername != "" {
			scorerNickName, err := s.getUserNickName(ctx, *scoreDB.ScorerUsername)
			if err == nil {
				scoreInfo.ScorerNickName = &scorerNickName
			}
		}

		// 获取部门名称
		departmentName, err := s.getDepartmentName(ctx, scoreDB.DepartmentId)
		if err == nil {
			scoreInfo.DepartmentName = departmentName
		}

		departmentScores = append(departmentScores, scoreInfo)
	}

	return departmentScores, nil
}

// getDepartmentName 根据部门ID获取部门名称
func (s *AssessmentDataService) getDepartmentName(ctx context.Context, departmentId int) (string, error) {
	var departmentName string
	err := global.GVA_DB.Table("org_org").
		Select("name").
		Where("id = ?", departmentId).
		Scan(&departmentName).Error

	if err != nil {
		return "", fmt.Errorf("获取部门名称失败: %v", err)
	}

	return departmentName, nil
}

// getCalculationParameters 获取计算参数映射信息
func (s *AssessmentDataService) getCalculationParameters(ctx context.Context) ([]assessmentReq.CalculationParameterInfo, error) {
	var parameters []assessmentReq.CalculationParameterInfo

	// 由于role_id现在是字符串类型（可能包含多个ID），我们不能直接JOIN
	// 先获取基本参数信息，角色名称在前端处理
	err := global.GVA_DB.Table("calculation_parameters cp").
		Select(`cp.id, cp.parameter_name, cp.parameter_name_cn, cp.role_id,
				'' as role_name,
				cp.assessment_category_id,
				COALESCE(ac.category_name, '') as assessment_category_name`).
		Joins("LEFT JOIN assessment_categories ac ON cp.assessment_category_id = ac.id").
		Order("cp.parameter_name").
		Scan(&parameters).Error

	if err != nil {
		return nil, fmt.Errorf("查询计算参数失败: %v", err)
	}

	return parameters, nil
}

// assembleUserParameterScores 组装用户参数评分数据
func (s *AssessmentDataService) assembleUserParameterScores(userName string, userInfo *assessmentReq.UserInfo, calcMethod *assessmentReq.CalculationMethod, parameterScores []assessmentReq.ParameterScoreDetail, requestedParams []string, validationDetails []assessmentReq.ValidationDetail) assessmentReq.UserParameterScores {
	userScores := assessmentReq.UserParameterScores{
		UserName:          userName,
		UserNickName:      userInfo.NickName,
		CalculationMethod: calcMethod,
		ParameterScores:   parameterScores,
		HasError:          false,
		ErrorMessage:      "",
		ValidationDetails: validationDetails,
	}

	// 设置校验状态和信息
	if len(validationDetails) > 0 {
		// 检查是否有错误
		hasError := false
		var errorMessages []string

		for _, detail := range validationDetails {
			if detail.Status == "error" {
				hasError = true
				errorMessages = append(errorMessages, detail.Message)
			}
		}

		if hasError {
			userScores.ValidationStatus = "error"
			userScores.ValidationMessage = strings.Join(errorMessages, "；")
			userScores.HasError = true
			userScores.ErrorMessage = userScores.ValidationMessage
		} else {
			userScores.ValidationStatus = "success"
			userScores.ValidationMessage = "校验通过"
		}
	} else {
		userScores.ValidationStatus = "success"
		userScores.ValidationMessage = "无需校验"
	}

	// 计算用户个人汇总信息
	userScores.UserSummary = s.calculateUserSummary(calcMethod, parameterScores, requestedParams)

	return userScores
}

// validateUserScoreCompleteness 校验用户评分完整性（返回校验结果而不是错误）
func (s *AssessmentDataService) validateUserScoreCompleteness(ctx context.Context, userName string, configId int, hasDepartmentManagerScore bool) []assessmentReq.ValidationDetail {
	var validationDetails []assessmentReq.ValidationDetail

	// 1. 检查该用户在此考核配置中是否有考核系数分配
	var coefficientCount int64
	err := global.GVA_DB.Model(&assessment.AssessmentCoefficientAllocation{}).
		Where("username = ? AND assessment_config_id = ?", userName, configId).
		Count(&coefficientCount).Error
	if err != nil {
		validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
			Type:    "coefficient_allocation",
			Status:  "error",
			Message: fmt.Sprintf("查询考核系数分配失败: %v", err),
		})
		return validationDetails
	}

	if coefficientCount == 0 {
		validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
			Type:    "coefficient_allocation",
			Status:  "error",
			Message: "该名用户需要考核系数分配，请先进行分配",
		})
		return validationDetails
	}

	// 2. 获取该用户的所有考核系数分配记录
	var coefficients []assessment.AssessmentCoefficientAllocation
	err = global.GVA_DB.Where("username = ? AND assessment_config_id = ?", userName, configId).
		Find(&coefficients).Error
	if err != nil {
		validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
			Type:    "coefficient_allocation",
			Status:  "error",
			Message: fmt.Sprintf("获取考核系数分配记录失败: %v", err),
		})
		return validationDetails
	}

	// 3. 检查每个项目的项目负责人评分情况
	for _, coeff := range coefficients {
		if coeff.ProjectId == nil {
			continue
		}

		// 检查该项目是否有对应的项目负责人评分
		var scoreCount int64
		err = global.GVA_DB.Model(&assessment.ProjectManagerScore{}).
			Where("coefficient_allocation_id = ?", coeff.Id).
			Count(&scoreCount).Error
		if err != nil {
			validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
				Type:      "project_manager_score",
				Status:    "error",
				Message:   fmt.Sprintf("查询项目负责人评分失败: %v", err),
				ProjectId: coeff.ProjectId,
			})
			continue
		}

		if scoreCount == 0 {
			// 获取项目名称和项目负责人信息
			projectName, managerName, managerId, err := s.getProjectInfoAndManagerWithId(ctx, *coeff.ProjectId)
			if err != nil {
				validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
					Type:      "project_manager_score",
					Status:    "error",
					Message:   fmt.Sprintf("获取项目信息失败: %v", err),
					ProjectId: coeff.ProjectId,
				})
				continue
			}

			// 检查项目负责人是否与被分配系数的用户是同一人
			// 如果是同一人，则跳过校验（项目负责人不需要给自己评分）
			if managerId != "" && managerId == userName {
				// 项目负责人是自己，跳过校验
				continue
			}

			validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
				Type:        "project_manager_score",
				Status:      "error",
				Message:     fmt.Sprintf("%s项目未评分-项目负责人%s", projectName, managerName),
				ProjectId:   coeff.ProjectId,
				ProjectName: projectName,
				ManagerName: managerName,
			})
		}
	}

	// 4. 检查机构负责人评分（如果需要）
	if hasDepartmentManagerScore {
		var deptScoreCount int64
		err = global.GVA_DB.Table("department_manager_score").
			Where("username = ? AND assessment_config_id = ?", userName, configId).
			Count(&deptScoreCount).Error
		if err != nil {
			validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
				Type:    "department_manager_score",
				Status:  "error",
				Message: fmt.Sprintf("查询部门经理评分失败: %v", err),
			})
		} else if deptScoreCount == 0 {
			validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
				Type:    "department_manager_score",
				Status:  "error",
				Message: "机构负责人暂无评分",
			})
		}
	}

	return validationDetails
}

// batchValidateUserScoreCompleteness 批量校验用户评分完整性
func (s *AssessmentDataService) batchValidateUserScoreCompleteness(ctx context.Context, userValidationMap map[string][]int) map[string][]assessmentReq.ValidationDetail {
	result := make(map[string][]assessmentReq.ValidationDetail)

	for userName, configIds := range userValidationMap {
		var allValidationDetails []assessmentReq.ValidationDetail

		for _, configId := range configIds {
			// 这里需要确定用户是否需要 department_manager_score 校验
			// 为了简化，我们先假设都需要，后续可以优化
			validationDetails := s.validateUserScoreCompleteness(ctx, userName, configId, true)
			allValidationDetails = append(allValidationDetails, validationDetails...)
		}

		result[userName] = allValidationDetails
	}

	return result
}

// getProjectInfoAndManager 获取项目信息和项目负责人
func (s *AssessmentDataService) getProjectInfoAndManager(ctx context.Context, projectId int) (string, string, error) {
	projectName, managerName, _, err := s.getProjectInfoAndManagerWithId(ctx, projectId)
	return projectName, managerName, err
}

// getProjectInfoAndManagerWithId 获取项目信息和项目负责人（包含负责人ID）
func (s *AssessmentDataService) getProjectInfoAndManagerWithId(ctx context.Context, projectId int) (string, string, string, error) {
	type ProjectInfo struct {
		Name      string  `json:"name"`
		ManagerId *string `json:"managerId"`
	}

	var project ProjectInfo
	err := global.GVA_DB.Table("project_info").
		Select("name, manager_id").
		Where("id = ?", projectId).
		First(&project).Error
	if err != nil {
		return "", "", "", fmt.Errorf("项目不存在: %v", err)
	}

	projectName := project.Name
	if projectName == "" {
		projectName = fmt.Sprintf("项目%d", projectId)
	}

	// 获取项目负责人姓名和ID
	var managerName string
	var managerId string
	if project.ManagerId != nil && *project.ManagerId != "" {
		managerId = *project.ManagerId
		// 从sys_users表获取用户昵称
		var user struct {
			NickName string `json:"nickName"`
		}
		err = global.GVA_DB.Table("sys_users").
			Select("nick_name").
			Where("username = ?", *project.ManagerId).
			First(&user).Error
		if err == nil && user.NickName != "" {
			managerName = user.NickName
		} else {
			// 如果找不到用户昵称，使用用户名
			managerName = *project.ManagerId
		}
	} else {
		managerName = "未知"
		managerId = ""
	}

	return projectName, managerName, managerId, nil
}

// calculateUserSummary 计算用户个人汇总信息
func (s *AssessmentDataService) calculateUserSummary(calcMethod *assessmentReq.CalculationMethod, parameterScores []assessmentReq.ParameterScoreDetail, requestedParams []string) assessmentReq.ParameterScoreSummary {
	summary := assessmentReq.ParameterScoreSummary{
		TotalParameters:    0,
		ScoredParameters:   0,
		UnscoredParameters: 0,
		TotalRecords:       len(parameterScores),
	}

	// 确定需要统计的参数范围
	var targetParams []string
	if len(requestedParams) > 0 {
		targetParams = requestedParams
	} else if calcMethod != nil {
		for _, param := range calcMethod.AssignedParameters {
			targetParams = append(targetParams, param.ParameterName)
		}
	}

	summary.TotalParameters = len(targetParams)

	// 统计已评分的参数
	scoredParams := make(map[string]bool)
	for _, score := range parameterScores {
		if score.ScoreValue != nil {
			scoredParams[score.ParameterName] = true
		}
	}

	summary.ScoredParameters = len(scoredParams)
	summary.UnscoredParameters = summary.TotalParameters - summary.ScoredParameters

	return summary
}

// calculateBatchSummary 计算批量查询汇总信息
func (s *AssessmentDataService) calculateBatchSummary(users []assessmentReq.UserParameterScores, totalRecords int) assessmentReq.BatchQuerySummary {
	summary := assessmentReq.BatchQuerySummary{
		TotalUsers:   len(users),
		SuccessUsers: 0,
		ErrorUsers:   0,
		TotalRecords: totalRecords,
	}

	for _, user := range users {
		if user.HasError {
			summary.ErrorUsers++
		} else {
			summary.SuccessUsers++
		}
	}

	return summary
}
