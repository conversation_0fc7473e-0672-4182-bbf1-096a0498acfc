/**
 * 计算集成模块 - 在 projectAssessmentScore.vue 中使用的集成函数
 * 提供简化的接口来使用动态计算功能
 */

import { DynamicCalculationService } from './dynamicCalculationService.js'
import { ElMessage } from 'element-plus'

// 创建全局计算服务实例
const calculationService = new DynamicCalculationService()

/**
 * 为员工评分表格中的用户计算最终得分
 * @param {string} userName - 用户名
 * @param {number} assessmentConfigId - 考核配置ID
 * @returns {Promise<number|null>} 计算结果
 */
export async function calculateUserFinalScore(userName, assessmentConfigId) {
  try {
    console.log('🧮 计算用户最终得分:', { userName, assessmentConfigId })

    const result = await calculationService.calculateWithUserMethod(userName, assessmentConfigId)
    
    if (result.success) {
      console.log('✅ 计算成功:', result.result)
      return result.result
    } else {
      console.error('❌ 计算失败:', result.error)
      ElMessage.warning(`用户 ${userName} 计算失败: ${result.error}`)
      return null
    }
  } catch (error) {
    console.error('❌ 计算用户最终得分时发生错误:', error)
    ElMessage.error(`计算失败: ${error.message}`)
    return null
  }
}

/**
 * 批量计算多个用户的最终得分
 * @param {Array} userNames - 用户名列表
 * @param {number} assessmentConfigId - 考核配置ID
 * @returns {Promise<Object>} 批量计算结果 {userName: score}
 */
export async function calculateBatchUserScores(userNames, assessmentConfigId) {
  try {
    console.log('🧮 批量计算用户得分:', { userNames, assessmentConfigId })

    const results = {}
    
    // 为每个用户单独计算（避免公式不同的问题）
    for (const userName of userNames) {
      const score = await calculateUserFinalScore(userName, assessmentConfigId)
      results[userName] = score
    }

    console.log('✅ 批量计算完成:', results)
    return results
  } catch (error) {
    console.error('❌ 批量计算用户得分时发生错误:', error)
    ElMessage.error(`批量计算失败: ${error.message}`)
    return {}
  }
}

/**
 * 使用自定义公式计算用户得分
 * @param {string} userName - 用户名
 * @param {number} assessmentConfigId - 考核配置ID
 * @param {string} customFormula - 自定义公式
 * @returns {Promise<number|null>} 计算结果
 */
export async function calculateWithCustomFormula(userName, assessmentConfigId, customFormula) {
  try {
    console.log('🧮 使用自定义公式计算:', { userName, assessmentConfigId, customFormula })

    const result = await calculationService.calculateForUser(userName, assessmentConfigId, customFormula)
    
    if (result.success) {
      console.log('✅ 自定义公式计算成功:', result.result)
      return result.result
    } else {
      console.error('❌ 自定义公式计算失败:', result.error)
      ElMessage.warning(`自定义公式计算失败: ${result.error}`)
      return null
    }
  } catch (error) {
    console.error('❌ 使用自定义公式计算时发生错误:', error)
    ElMessage.error(`计算失败: ${error.message}`)
    return null
  }
}

/**
 * 验证公式语法
 * @param {string} formula - 公式字符串
 * @returns {Object} 验证结果
 */
export function validateFormulaSync(formula) {
  try {
    return calculationService.validateFormula(formula)
  } catch (error) {
    console.error('❌ 验证公式时发生错误:', error)
    return {
      isValid: false,
      error: error.message,
      extractedParameters: [],
      testResult: null
    }
  }
}

/**
 * 获取用户的详细计算信息（用于调试和显示）
 * @param {string} userName - 用户名
 * @param {number} assessmentConfigId - 考核配置ID
 * @returns {Promise<Object|null>} 详细计算信息
 */
export async function getUserCalculationDetails(userName, assessmentConfigId) {
  try {
    console.log('🔍 获取用户计算详情:', { userName, assessmentConfigId })

    const result = await calculationService.calculateWithUserMethod(userName, assessmentConfigId)
    
    if (result.success) {
      return result.details
    } else {
      console.error('❌ 获取计算详情失败:', result.error)
      return null
    }
  } catch (error) {
    console.error('❌ 获取用户计算详情时发生错误:', error)
    return null
  }
}

/**
 * 在员工评分表格中添加计算得分列
 * @param {Array} employeeData - 员工数据数组
 * @param {number} assessmentConfigId - 考核配置ID
 * @returns {Promise<Array>} 添加了计算得分的员工数据
 */
export async function addCalculatedScoresToEmployeeData(employeeData, assessmentConfigId) {
  try {
    console.log('🧮 为员工数据添加计算得分:', { employeeCount: employeeData.length, assessmentConfigId })

    const updatedData = []
    
    for (const employee of employeeData) {
      const calculatedScore = await calculateUserFinalScore(employee.username, assessmentConfigId)
      
      updatedData.push({
        ...employee,
        calculatedScore: calculatedScore,
        hasCalculatedScore: calculatedScore !== null
      })
    }

    console.log('✅ 员工数据计算得分添加完成')
    return updatedData
  } catch (error) {
    console.error('❌ 为员工数据添加计算得分时发生错误:', error)
    ElMessage.error(`添加计算得分失败: ${error.message}`)
    return employeeData
  }
}

/**
 * 实时计算并更新用户得分（用于实时显示）
 * @param {string} userName - 用户名
 * @param {number} assessmentConfigId - 考核配置ID
 * @param {Function} updateCallback - 更新回调函数
 */
export async function calculateAndUpdateUserScore(userName, assessmentConfigId, updateCallback) {
  try {
    const score = await calculateUserFinalScore(userName, assessmentConfigId)
    
    if (typeof updateCallback === 'function') {
      updateCallback(userName, score)
    }
    
    return score
  } catch (error) {
    console.error('❌ 实时计算并更新用户得分时发生错误:', error)
    if (typeof updateCallback === 'function') {
      updateCallback(userName, null)
    }
    return null
  }
}

/**
 * 导出计算结果到Excel格式的数据
 * @param {Array} userNames - 用户名列表
 * @param {number} assessmentConfigId - 考核配置ID
 * @returns {Promise<Array>} Excel导出数据
 */
export async function exportCalculationResults(userNames, assessmentConfigId) {
  try {
    console.log('📊 导出计算结果:', { userNames, assessmentConfigId })

    const exportData = []
    
    for (const userName of userNames) {
      const result = await calculationService.calculateWithUserMethod(userName, assessmentConfigId)
      
      if (result.success) {
        const details = result.details
        exportData.push({
          '用户名': userName,
          '用户昵称': details.calculationMethod?.methodName || '未知',
          '计算公式': details.calculationMethod?.formula || '无',
          '计算结果': result.result,
          '参数值': JSON.stringify(details.parameterValues),
          '计算状态': '成功'
        })
      } else {
        exportData.push({
          '用户名': userName,
          '用户昵称': '未知',
          '计算公式': '无',
          '计算结果': null,
          '参数值': '无',
          '计算状态': `失败: ${result.error}`
        })
      }
    }

    console.log('✅ 计算结果导出数据准备完成')
    return exportData
  } catch (error) {
    console.error('❌ 导出计算结果时发生错误:', error)
    ElMessage.error(`导出失败: ${error.message}`)
    return []
  }
}

/**
 * 获取计算服务实例（用于高级用法）
 * @returns {DynamicCalculationService} 计算服务实例
 */
export function getCalculationService() {
  return calculationService
}

// 导出常用的计算函数
export {
  calculateUserFinalScore,
  calculateBatchUserScores,
  calculateWithCustomFormula,
  validateFormulaSync,
  getUserCalculationDetails,
  addCalculatedScoresToEmployeeData,
  calculateAndUpdateUserScore,
  exportCalculationResults
}
