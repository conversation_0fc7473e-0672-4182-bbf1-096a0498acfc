# COUNT函数调试说明

## 问题分析

错误信息：`COUNT函数需要1个参数`

这表明在解析`COUNT(project_data) > 0`时，COUNT函数没有正确接收到参数。

## 可能的原因

### 1. 函数参数解析问题
在`processFunctions`方法中，正则表达式`([A-Z_]+)\s*\(([^)]*)\)`可能没有正确捕获参数。

### 2. 参数字符串处理问题
`parseArguments`方法可能没有正确处理参数字符串。

### 3. 变量名解析问题
`project_data`参数名可能没有正确传递给COUNT函数。

## 调试步骤

### 1. 检查函数解析过程
现在代码中已经添加了详细的调试日志：

```go
fmt.Printf("解析函数: %s, 参数字符串: '%s'\n", funcName, argsStr)
fmt.Printf("解析后的参数: %v, 参数数量: %d\n", args, len(args))
fmt.Printf("COUNT函数接收到的参数: %v, 参数数量: %d\n", args, len(args))
```

### 2. 预期的调试输出
对于表达式`COUNT(project_data) > 0`，应该看到：

```
解析函数: COUNT, 参数字符串: 'project_data'
解析后的参数: [project_data], 参数数量: 1
COUNT函数接收到的参数: [project_data], 参数数量: 1
COUNT函数处理参数: 'project_data'
找到参数 project_data, 值类型: []interface{}, 值: [...]
数组长度: 2
```

### 3. 如果参数数量为0
可能的原因：
- 正则表达式没有正确匹配
- 参数字符串为空
- parseArguments方法返回空数组

## 临时解决方案

如果问题仍然存在，可以尝试以下方法：

### 1. 简化条件表达式
将`COUNT(project_data) > 0`改为更简单的条件：
```
project_data != null
```

### 2. 使用不同的函数
```
LENGTH(project_data) > 0
```

### 3. 直接使用变量
```
project_data
```

## 测试数据验证

确保测试数据格式正确：

```json
{
  "department_manager_score": 90,
  "project_data": [
    {
      "project_manager_score": 85,
      "project_participation": 0.8,
      "is_leader_of_this_project": false
    }
  ]
}
```

## 后端日志查看

在后端控制台中查看详细的调试日志：

1. 启动后端服务
2. 执行规则测试
3. 查看控制台输出的调试信息
4. 根据日志确定问题所在

## 可能的修复方案

### 1. 如果参数解析失败
检查正则表达式是否正确匹配函数调用。

### 2. 如果参数为空
检查`parseArguments`方法的实现。

### 3. 如果变量不存在
检查变量名是否正确传递到表达式解析器。

## 预期修复效果

修复后，测试应该显示：

```
测试执行成功
计算结果: 61.0
匹配条件: 项目数据存在时计算
执行详情:
- 条件评估: COUNT(project_data) > 0
- 结果: true
- 公式计算: CALCULATE_PROJECT_AVG(project_data, department_manager_score)
- 最终结果: 61.0
```

## 调试命令

如果需要手动测试，可以在后端添加临时测试代码：

```go
func TestCountFunction() {
    variables := map[string]interface{}{
        "project_data": []interface{}{
            map[string]interface{}{
                "project_manager_score": 85.0,
                "project_participation": 0.8,
                "is_leader_of_this_project": false,
            },
        },
    }
    
    parser := NewExpressionParser(variables)
    result, err := parser.EvaluateCondition("COUNT(project_data) > 0")
    
    fmt.Printf("测试结果: %t, 错误: %v\n", result, err)
}
```

---

通过这些调试步骤，应该能够定位并解决COUNT函数参数解析的问题。
