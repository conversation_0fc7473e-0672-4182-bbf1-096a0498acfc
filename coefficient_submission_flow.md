# 考核系数提交流程说明

## 修改概述

已将 `projectAssessmentScore.vue` 页面的提交逻辑修改为使用新的表结构，计算参数固定为 `project_participation`。

## 数据流程

### 1. 前端数据准备 (`prepareSubmitData`)

```javascript
// 准备提交数据
const prepareSubmitData = (tabName, tabData) => {
  const submitRecords = []
  
  tabData.members.forEach(member => {
    tabData.projects.forEach(project => {
      const coefficient = tabData.scores[`${project.id}-${member.id}`] || 0
      
      if (coefficient > 0) {
        submitRecords.push({
          assessmentConfigId: assessmentConfigId,
          username: member.userName || member.name,
          projectId: project.id,
          assessmentCoefficient: coefficient,
          calculationParameter: 'project_participation', // 固定值
          scorerUsername: currentUserName,
          createdAt: new Date().toISOString()
        })
      }
    })
  })
  
  return {
    assessmentConfigId: assessmentConfigId,
    records: submitRecords
  }
}
```

### 2. API 调用 (`replaceAssessmentCoefficients`)

**请求路径**: `POST /assessment/coefficients/replace`

**请求数据格式**:
```json
{
  "assessmentConfigId": 1,
  "records": [
    {
      "assessmentConfigId": 1,
      "username": "10112531",
      "projectId": 1,
      "assessmentCoefficient": 0.8,
      "calculationParameter": "project_participation",
      "scorerUsername": "current_user",
      "createdAt": "2025-01-15T10:00:00.000Z"
    }
  ]
}
```

### 3. 后端处理 (`ReplaceAssessmentCoefficients`)

#### 3.1 事务开始
- 删除现有的项目负责人评分记录
- 删除现有的系数分配记录

#### 3.2 数据插入
将前端数据转换并插入到 `assessment_coefficient_allocation` 表：

```go
coefficientAllocation := assessment.AssessmentCoefficientAllocation{
    AssessmentConfigId:    &assessmentConfigId,
    Username:              &username,
    ProjectId:             &projectId,
    AssessmentCoefficient: &assessmentCoefficient,
    CalculationParameter:  &calculationParameter, // "project_participation"
}
```

#### 3.3 数据验证
- 验证系数范围 (0-100)
- 验证用户系数总和为 100%

### 4. 数据库表结构

#### `assessment_coefficient_allocation` 表
```sql
CREATE TABLE `assessment_coefficient_allocation` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `assessment_config_id` bigint UNSIGNED NOT NULL,
  `username` varchar(50) NOT NULL,
  `project_id` bigint UNSIGNED NOT NULL,
  `assessment_coefficient` decimal(5,2) NOT NULL,
  `calculation_parameter` varchar(10) NOT NULL, -- 固定为 'project_participation'
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

#### `project_manager_score` 表
```sql
CREATE TABLE `project_manager_score` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `coefficient_allocation_id` bigint UNSIGNED NOT NULL,
  `manager_score` decimal(5,2) NOT NULL,
  `scorer_username` varchar(50) NOT NULL,
  `calculation_parameter` varchar(10) NOT NULL,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

## 关键修改点

### 1. 前端修改
- **文件**: `web/src/view/score/projectAssessmentScore/projectAssessmentScore.vue`
- **修改**: `calculationParameter: 'project_participation'` (第1027行)

### 2. 后端修改
- **文件**: `server/service/assessment/assessment_coefficient.go`
- **修改**: `ReplaceAssessmentCoefficients` 方法使用新表结构

### 3. 数据库迁移
- **文件**: `server/initialize/gorm_biz.go`
- **修改**: 添加 `AssessmentCoefficientAllocation` 到自动迁移

## 测试验证

### 1. 功能测试
1. 打开 `projectAssessmentScore.vue` 页面
2. 填写考核系数分配数据
3. 点击提交按钮
4. 验证数据是否正确插入到 `assessment_coefficient_allocation` 表
5. 验证 `calculation_parameter` 字段值为 `project_participation`

### 2. 数据验证
```sql
-- 查询提交的数据
SELECT * FROM assessment_coefficient_allocation 
WHERE calculation_parameter = 'project_participation'
ORDER BY created_at DESC;
```

### 3. API 测试
使用 Postman 或其他工具测试 `/assessment/coefficients/replace` 接口：

```bash
curl -X POST http://localhost:8888/assessment/coefficients/replace \
  -H "Content-Type: application/json" \
  -d '{
    "assessmentConfigId": 1,
    "records": [{
      "assessmentConfigId": 1,
      "username": "test_user",
      "projectId": 1,
      "assessmentCoefficient": 50.0,
      "calculationParameter": "project_participation",
      "scorerUsername": "admin"
    }]
  }'
```

## 注意事项

1. **计算参数固定**: 所有通过此页面提交的数据，`calculationParameter` 都固定为 `project_participation`
2. **事务处理**: 使用数据库事务确保数据一致性
3. **数据验证**: 后端会验证系数范围和总和
4. **向后兼容**: 查询接口仍然返回兼容的数据格式

## 后续工作

1. 测试新的提交流程
2. 验证数据完整性
3. 确认前端显示正常
4. 考虑是否需要数据迁移脚本将旧数据迁移到新表
