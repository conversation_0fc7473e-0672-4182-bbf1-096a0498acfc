/**
 * 动态计算服务 - 集成公式计算器和参数映射器
 * 根据 getUserParameterScores 接口返回的数据动态计算公式结果
 */

import { FormulaCalculator } from './formulaCalculator.js'
import { ParameterMapper } from './parameterMapper.js'
import { getSingleUserParameterScores, getBatchUserParameterScores } from '@/api/assessment/assessmentData'

class DynamicCalculationService {
  constructor() {
    this.formulaCalculator = new FormulaCalculator()
    this.parameterMapper = new ParameterMapper()
  }

  /**
   * 为单个用户计算公式结果
   * @param {string} userName - 用户名
   * @param {number} assessmentConfigId - 考核配置ID
   * @param {string} formula - 计算公式
   * @returns {Promise<Object>} 计算结果 {success: boolean, result: number, error: string, details: Object}
   */
  async calculateForUser(userName, assessmentConfigId, formula) {
    try {
      console.log('🚀 开始为用户计算公式:', { userName, assessmentConfigId, formula })

      // 1. 获取用户参数评分数据
      const response = await getSingleUserParameterScores(
        userName,
        assessmentConfigId ? [assessmentConfigId] : [],
        [] // 获取所有参数
      )

      if (response.code !== 0) {
        throw new Error(`获取用户参数评分失败: ${response.msg}`)
      }

      const userData = response.data.users[0]
      if (!userData) {
        throw new Error(`未找到用户 ${userName} 的数据`)
      }

      // 2. 提取参数值映射
      const parameterValues = this.parameterMapper.extractParameterValues(userData)

      // 3. 从公式中提取需要的参数
      const requiredParameters = this.parameterMapper.extractParameterNamesFromFormula(formula)

      // 4. 验证参数完整性
      const validation = this.parameterMapper.validateParameterMapping(parameterValues, requiredParameters)

      // 5. 计算公式
      const result = this.formulaCalculator.calculate(formula, parameterValues)

      const calculationResult = {
        success: true,
        result: result,
        error: null,
        details: {
          userName: userName,
          assessmentConfigId: assessmentConfigId,
          formula: formula,
          parameterValues: this.parameterMapper.formatParameterValues(parameterValues),
          requiredParameters: requiredParameters,
          validation: validation,
          calculationMethod: userData.calculationMethod,
          userSummary: userData.userSummary
        }
      }

      console.log('✅ 用户公式计算完成:', calculationResult)
      return calculationResult

    } catch (error) {
      console.error('❌ 用户公式计算失败:', error)
      return {
        success: false,
        result: null,
        error: error.message,
        details: {
          userName: userName,
          assessmentConfigId: assessmentConfigId,
          formula: formula
        }
      }
    }
  }

  /**
   * 为多个用户批量计算公式结果
   * @param {Array} userNames - 用户名列表
   * @param {number} assessmentConfigId - 考核配置ID
   * @param {string} formula - 计算公式
   * @returns {Promise<Object>} 批量计算结果
   */
  async calculateForUsers(userNames, assessmentConfigId, formula) {
    try {
      console.log('🚀 开始批量计算公式:', { userNames, assessmentConfigId, formula })

      // 1. 批量获取用户参数评分数据
      const response = await getBatchUserParameterScores(
        userNames,
        assessmentConfigId ? [assessmentConfigId] : [],
        [] // 获取所有参数
      )

      if (response.code !== 0) {
        throw new Error(`批量获取用户参数评分失败: ${response.msg}`)
      }

      const batchData = response.data
      const results = []

      // 2. 为每个用户计算公式
      for (const userName of userNames) {
        try {
          // 提取用户数据
          const parameterValues = this.parameterMapper.extractParameterValuesForUser(batchData, userName)
          
          // 从公式中提取需要的参数
          const requiredParameters = this.parameterMapper.extractParameterNamesFromFormula(formula)
          
          // 验证参数完整性
          const validation = this.parameterMapper.validateParameterMapping(parameterValues, requiredParameters)
          
          // 计算公式
          const result = this.formulaCalculator.calculate(formula, parameterValues)
          
          // 获取用户详细信息
          const userData = batchData.users.find(user => user.userName === userName)
          
          results.push({
            userName: userName,
            success: true,
            result: result,
            error: null,
            details: {
              parameterValues: this.parameterMapper.formatParameterValues(parameterValues),
              requiredParameters: requiredParameters,
              validation: validation,
              calculationMethod: userData?.calculationMethod,
              userSummary: userData?.userSummary
            }
          })

        } catch (userError) {
          console.error(`❌ 用户 ${userName} 计算失败:`, userError)
          results.push({
            userName: userName,
            success: false,
            result: null,
            error: userError.message,
            details: {}
          })
        }
      }

      const batchResult = {
        success: true,
        results: results,
        summary: {
          totalUsers: userNames.length,
          successCount: results.filter(r => r.success).length,
          errorCount: results.filter(r => !r.success).length,
          formula: formula,
          assessmentConfigId: assessmentConfigId
        },
        batchData: batchData
      }

      console.log('✅ 批量公式计算完成:', batchResult)
      return batchResult

    } catch (error) {
      console.error('❌ 批量公式计算失败:', error)
      return {
        success: false,
        results: [],
        error: error.message,
        summary: {
          totalUsers: userNames.length,
          successCount: 0,
          errorCount: userNames.length,
          formula: formula,
          assessmentConfigId: assessmentConfigId
        }
      }
    }
  }

  /**
   * 根据用户的计算方法自动计算
   * @param {string} userName - 用户名
   * @param {number} assessmentConfigId - 考核配置ID
   * @returns {Promise<Object>} 计算结果
   */
  async calculateWithUserMethod(userName, assessmentConfigId) {
    try {
      console.log('🚀 根据用户计算方法自动计算:', { userName, assessmentConfigId })

      // 1. 获取用户参数评分数据
      const response = await getSingleUserParameterScores(
        userName,
        assessmentConfigId ? [assessmentConfigId] : [],
        []
      )

      if (response.code !== 0) {
        throw new Error(`获取用户参数评分失败: ${response.msg}`)
      }

      const userData = response.data.users[0]
      if (!userData) {
        throw new Error(`未找到用户 ${userName} 的数据`)
      }

      // 2. 检查用户是否有计算方法
      if (!userData.calculationMethod || !userData.calculationMethod.formula) {
        throw new Error(`用户 ${userName} 未分配计算方法或公式为空`)
      }

      const formula = userData.calculationMethod.formula

      // 3. 使用用户的计算方法公式进行计算
      return await this.calculateForUser(userName, assessmentConfigId, formula)

    } catch (error) {
      console.error('❌ 根据用户计算方法自动计算失败:', error)
      return {
        success: false,
        result: null,
        error: error.message,
        details: {
          userName: userName,
          assessmentConfigId: assessmentConfigId
        }
      }
    }
  }

  /**
   * 验证公式语法
   * @param {string} formula - 计算公式
   * @returns {Object} 验证结果 {isValid: boolean, error: string, extractedParameters: Array}
   */
  validateFormula(formula) {
    try {
      // 提取参数
      const extractedParameters = this.parameterMapper.extractParameterNamesFromFormula(formula)
      
      // 创建测试参数映射（所有参数设为1）
      const testParameters = {}
      extractedParameters.forEach(param => {
        testParameters[param] = 1
      })

      // 尝试计算
      const result = this.formulaCalculator.calculate(formula, testParameters)

      return {
        isValid: true,
        error: null,
        extractedParameters: extractedParameters,
        testResult: result
      }

    } catch (error) {
      return {
        isValid: false,
        error: error.message,
        extractedParameters: [],
        testResult: null
      }
    }
  }
}

export { DynamicCalculationService }
