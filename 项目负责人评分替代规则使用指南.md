# 项目负责人评分替代规则使用指南

## 业务背景

在项目评分系统中，存在以下业务规则：
- 标准计算公式：`department_manager_score * 0.6 + AVG(project_manager_score * project_participation) * 0.4`
- 特殊情况：当某人是项目负责人时，不能为自己评分，需要用部门经理评分替代项目经理评分

## 技术实现

### 1. 新增自定义函数

我们在表达式解析器中新增了 `CALCULATE_PROJECT_AVG` 函数来处理这种复杂的业务逻辑。

**函数签名：**
```
CALCULATE_PROJECT_AVG(project_data, department_manager_score)
```

**参数说明：**
- `project_data`: 项目数据数组，包含每个项目的评分信息
- `department_manager_score`: 部门经理评分，用于替代项目负责人的自评

### 2. 数据结构

**project_data 数组结构：**
```json
[
  {
    "project_manager_score": 85,        // 项目经理评分
    "project_participation": 0.8,       // 项目参与度
    "is_leader_of_this_project": false  // 是否为该项目的负责人
  },
  {
    "project_manager_score": 88,
    "project_participation": 0.6,
    "is_leader_of_this_project": true   // 该项目的负责人
  }
]
```

### 3. 计算逻辑

函数内部处理逻辑：
```
对于每个项目：
  如果 is_leader_of_this_project == true:
    计算值 = department_manager_score * project_participation
  否则:
    计算值 = project_manager_score * project_participation

最终结果 = 所有项目计算值的平均值
```

## 规则配置

### 1. 输入参数配置

在规则引擎界面中配置以下参数：

```json
{
  "inputParameters": [
    {
      "name": "department_manager_score",
      "type": "number",
      "required": true,
      "description": "部门经理评分"
    },
    {
      "name": "project_data",
      "type": "array",
      "required": true,
      "description": "项目数据数组，包含项目经理评分、参与度、是否为负责人"
    }
  ]
}
```

### 2. 规则条件配置

```json
{
  "conditions": [
    {
      "id": "condition_1",
      "name": "项目评分计算（负责人替代逻辑）",
      "when": "COUNT(project_data) > 0",
      "then": {
        "formula": "department_manager_score * 0.6 + CALCULATE_PROJECT_AVG(project_data, department_manager_score) * 0.4"
      },
      "priority": 1,
      "description": "使用CALCULATE_PROJECT_AVG函数处理项目负责人的评分替代逻辑"
    }
  ]
}
```

## 使用示例

### 1. 测试数据

```json
{
  "department_manager_score": 90,
  "project_data": [
    {
      "project_manager_score": 85,
      "project_participation": 0.8,
      "is_leader_of_this_project": false
    },
    {
      "project_manager_score": 88,
      "project_participation": 0.6,
      "is_leader_of_this_project": true
    },
    {
      "project_manager_score": 92,
      "project_participation": 0.9,
      "is_leader_of_this_project": false
    }
  ]
}
```

### 2. 计算过程

**步骤1：计算每个项目的加权评分**
- 项目1：85 * 0.8 = 68（普通成员，使用项目经理评分）
- 项目2：90 * 0.6 = 54（项目负责人，使用部门经理评分替代）
- 项目3：92 * 0.9 = 82.8（普通成员，使用项目经理评分）

**步骤2：计算平均值**
- 平均值：(68 + 54 + 82.8) / 3 = 68.27

**步骤3：应用最终公式**
- 最终结果：90 * 0.6 + 68.27 * 0.4 = 54 + 27.31 = 81.31

### 3. API调用示例

```javascript
const result = await calculateByRule({
  methodId: 4, // 项目负责人评分替代规则的ID
  parameters: {
    department_manager_score: 90,
    project_data: [
      {
        project_manager_score: 85,
        project_participation: 0.8,
        is_leader_of_this_project: false
      },
      {
        project_manager_score: 88,
        project_participation: 0.6,
        is_leader_of_this_project: true
      }
    ]
  }
})

console.log('计算结果:', result.data.result) // 81.31
```

## 界面操作指南

### 1. 创建规则

1. 进入"考核管理" -> "计算方法"页面
2. 选择考核类别，点击"新建计算方法"
3. 选择配置模式为"规则引擎"
4. 配置输入参数：
   - 添加 `department_manager_score` (number类型)
   - 添加 `project_data` (array类型)

### 2. 配置公式

在公式编辑器中输入：
```
department_manager_score * 0.6 + CALCULATE_PROJECT_AVG(project_data, department_manager_score) * 0.4
```

或者点击"业务函数"按钮中的"CALCULATE_PROJECT_AVG()"来快速插入。

### 3. 测试规则

在测试面板中：
1. 点击"完整数据"按钮填充示例数据
2. 点击"执行测试"查看计算结果
3. 查看执行详情了解计算过程

## 注意事项

### 1. 数据格式要求

- `project_data` 必须是数组格式
- 数组中每个元素必须包含必要的字段
- `is_leader_of_this_project` 字段必须是布尔类型

### 2. 错误处理

- 如果 `project_data` 为空数组，函数返回 0
- 如果数据格式不正确，会返回详细的错误信息
- 建议在生产环境使用前充分测试

### 3. 性能考虑

- 函数会遍历整个 `project_data` 数组
- 对于大量项目数据，建议在前端进行适当的数据预处理
- 可以考虑添加缓存机制优化性能

## 扩展性

这个自定义函数的设计具有良好的扩展性：

1. **支持更多字段**：可以在 `project_data` 中添加更多业务字段
2. **支持复杂逻辑**：可以在函数内部实现更复杂的业务规则
3. **支持其他函数**：可以参考这个实现添加更多自定义业务函数

通过这种方式，我们成功地将复杂的业务逻辑封装在了规则引擎中，既保持了灵活性，又确保了业务逻辑的正确性。
