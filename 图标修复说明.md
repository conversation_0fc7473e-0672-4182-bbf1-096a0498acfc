# 图标导入问题修复说明

## 问题描述
在使用规则引擎组件时，可能会遇到Element Plus图标导入错误，如：
```
The requested module does not provide an export named 'PlayArrow'
```

## 解决方案

### 1. 已修复的文件
- ✅ `RuleTestPanel.vue` - 将 `PlayArrow` 改为 `CaretRight`

### 2. 常用的Element Plus图标替换对照表

| 原图标名 | 替换图标名 | 用途 |
|---------|-----------|------|
| PlayArrow | CaretRight | 播放/执行按钮 |
| Add | Plus | 添加按钮 |
| Delete | Delete | 删除按钮 |
| Edit | Edit | 编辑按钮 |
| Save | Check | 保存按钮 |
| Cancel | Close | 取消按钮 |
| Search | Search | 搜索按钮 |
| Refresh | Refresh | 刷新按钮 |

### 3. 如何查找可用图标

1. 访问 Element Plus 官方文档的图标页面
2. 或者在项目中使用以下代码查看所有可用图标：

```javascript
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
console.log(Object.keys(ElementPlusIconsVue))
```

### 4. 正确的图标导入方式

```vue
<template>
  <el-button>
    <el-icon><CaretRight /></el-icon>
    执行测试
  </el-button>
</template>

<script setup>
import { CaretRight } from '@element-plus/icons-vue'
</script>
```

### 5. 批量替换命令（如果需要）

如果遇到更多图标问题，可以使用以下PowerShell命令批量替换：

```powershell
# 替换PlayArrow为CaretRight
(Get-Content "path/to/file.vue") -replace "PlayArrow", "CaretRight" | Set-Content "path/to/file.vue"
```

## 验证修复

1. 重启开发服务器
2. 清除浏览器缓存
3. 检查控制台是否还有图标相关错误
4. 测试规则引擎功能是否正常

## 预防措施

1. 在使用新图标前，先在Element Plus文档中确认图标名称
2. 使用TypeScript可以提供更好的类型检查
3. 建立项目的图标使用规范文档

---

修复完成后，规则引擎的所有功能应该能够正常使用！
