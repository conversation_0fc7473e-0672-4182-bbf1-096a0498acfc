# 基于JSON配置的热更新规则引擎方案

## 方案概述

本方案旨在解决当前评分计算系统中业务规则硬编码、难以灵活配置的问题。通过设计一个基于JSON配置的规则引擎，实现业务规则与代码逻辑的完全分离，支持热更新和可视化配置管理。

## 核心设计理念

### 1. 配置驱动的计算模式
- **传统模式**：代码驱动计算，规则变更需要修改源码、测试、发布
- **新模式**：配置驱动计算，业务逻辑抽象为可配置的JSON规则
- **优势**：业务规则与代码逻辑完全分离，支持热更新

### 2. 统一计算入口
- 所有业务逻辑通过同一个API入口处理
- 前端只需要知道规则ID和参数
- 后端根据规则配置执行相应的计算逻辑

### 3. 系统架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端调用       │    │   规则引擎API    │    │   规则缓存       │
│                │────▶│                │────▶│                │
│ calculateByRule │    │ /rule/calculate │    │ 内存缓存+热更新  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   规则计算器     │    │   数据库存储     │
                       │                │────▶│                │
                       │ RuleCalculator  │    │ calculation_rules│
                       └─────────────────┘    └─────────────────┘
```

## 数据库设计

### 主规则表 (calculation_rules)
```sql
CREATE TABLE `calculation_rules` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `rule_id` varchar(100) NOT NULL COMMENT '规则唯一标识',
  `name` varchar(200) NOT NULL COMMENT '规则名称',
  `rule_config` json NOT NULL COMMENT '规则配置JSON',
  `category` varchar(50) DEFAULT NULL COMMENT '规则分类',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `version` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `priority` int DEFAULT 0 COMMENT '优先级',
  `created_by` varchar(50) DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_id` (`rule_id`),
  KEY `idx_category_active` (`category`, `is_active`)
);
```

### 规则历史表 (rule_history)
```sql
CREATE TABLE `rule_history` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `rule_id` varchar(100) NOT NULL COMMENT '关联规则ID',
  `version` varchar(20) NOT NULL COMMENT '历史版本号',
  `rule_snapshot` json NOT NULL COMMENT '规则快照',
  `change_description` varchar(500) DEFAULT NULL COMMENT '变更说明',
  `created_by` varchar(50) DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_rule_version` (`rule_id`, `version`)
);
```

## 规则配置结构

### 基础结构示例
```json
{
  "ruleId": "project_comprehensive_score_v3",
  "metadata": {
    "name": "项目综合评分计算V3",
    "description": "支持多项目、多评分者的综合评分计算",
    "author": "张三",
    "lastModified": "2024-01-15T10:30:00Z",
    "tags": ["评分", "项目", "综合计算"]
  },
  "variables": {
    "projectWeight": {
      "value": 0.4,
      "type": "number",
      "description": "项目评分权重",
      "constraints": {"min": 0, "max": 1}
    },
    "departmentWeight": {
      "value": 0.6, 
      "type": "number",
      "description": "部门评分权重"
    }
  },
  "parameters": {
    "username": {
      "source": "input",
      "type": "string",
      "required": true,
      "description": "被评价用户名"
    },
    "projectScores": {
      "source": "query",
      "sql": "SELECT manager_score FROM project_manager_score WHERE username = ?",
      "params": ["${input.username}"],
      "type": "array",
      "cache": true,
      "cacheTTL": 300
    },
    "avgProjectScore": {
      "source": "calculation",
      "formula": "AVG(${query.projectScores})",
      "type": "number",
      "condition": "${query.projectScores.length} > 0"
    }
  },
  "conditions": [
    {
      "id": "condition_1",
      "name": "双重评分场景",
      "when": "projectScore != null && departmentScore != null",
      "then": {
        "formula": "projectScore * projectWeight + departmentScore * departmentWeight",
        "postProcess": [
          {"type": "multiply", "value": "coefficient"},
          {"type": "round", "decimals": 2}
        ]
      },
      "priority": 1
    }
  ],
  "businessRules": [
    {
      "name": "高分配额检查",
      "type": "validation",
      "when": "calculatedScore >= 95",
      "action": {
        "type": "checkQuota",
        "parameters": {
          "quotaType": "highScore",
          "departmentId": "parameters.departmentId"
        },
        "onFailure": {
          "type": "error",
          "message": "高分配额已用完"
        }
      }
    }
  ]
}
```

## 参数来源机制

### 参数来源分类

#### 1. 直接输入参数 (Input Parameters)
前端调用API时直接传入的参数：
```javascript
const result = await calculateByRule({
  ruleId: "score_calculation_v1",
  parameters: {
    projectScore: 85,
    departmentScore: 90,
    username: "zhangsan"
  }
})
```

#### 2. 数据库查询参数 (Query Parameters)
通过SQL查询从数据库获取的参数：
```json
{
  "userInfo": {
    "source": "query",
    "sql": "SELECT nick_name, department_id FROM sys_users WHERE username = ?",
    "params": ["${input.username}"],
    "type": "object",
    "cache": true,
    "cacheTTL": 300
  }
}
```

#### 3. 计算参数 (Calculated Parameters)
基于其他参数计算得出的参数：
```json
{
  "avgProjectScore": {
    "source": "calculation",
    "formula": "AVG(${query.projectScores})",
    "type": "number"
  }
}
```

#### 4. 上下文参数 (Context Parameters)
系统级别的参数：
```json
{
  "currentTime": {
    "source": "context",
    "provider": "system.currentTime",
    "type": "datetime"
  }
}
```

#### 5. 配置参数 (Configuration Parameters)
从配置表获取的参数：
```json
{
  "scoringWeights": {
    "source": "config",
    "configKey": "scoring.weights.v2",
    "defaultValue": {"project": 0.4, "department": 0.6},
    "type": "object"
  }
}
```

### 参数获取执行流程
```
1. Input Parameters (直接输入)
   ↓
2. Context Parameters (系统上下文)
   ↓  
3. Configuration Parameters (配置参数)
   ↓
4. Query Parameters (数据库查询)
   ↓
5. Calculated Parameters (计算参数)
   ↓
6. Rule Execution (规则执行)
```

## API接口设计

### 核心计算接口
```http
POST /api/v1/rule/calculate
Content-Type: application/json

{
  "ruleId": "project_score_v3",
  "parameters": {
    "username": "zhangsan",
    "assessmentId": 5,
    "coefficient": 1.2
  },
  "options": {
    "includeTrace": true,
    "validateOnly": false,
    "dryRun": false
  }
}
```

### 响应格式
```json
{
  "success": true,
  "result": {
    "finalScore": 102.0,
    "bonusAmount": 1530.0,
    "grade": "A"
  },
  "execution": {
    "ruleVersion": "3.1",
    "appliedCondition": "condition_1",
    "executionTimeMs": 15,
    "trace": [
      {
        "step": "condition_evaluation",
        "condition": "projectScore != null && departmentScore != null",
        "result": true
      },
      {
        "step": "formula_calculation", 
        "formula": "85 * 0.4 + 90 * 0.6",
        "result": 88.0
      }
    ]
  },
  "warnings": [],
  "metadata": {
    "ruleName": "项目综合评分计算V3",
    "lastModified": "2024-01-15T10:30:00Z"
  }
}
```

### 规则管理接口
```http
# 创建/更新规则
POST /api/v1/rule/config
{
  "ruleId": "bonus_calculation",
  "name": "奖金计算规则",
  "ruleConfig": { /* JSON配置 */ }
}

# 获取规则列表
GET /api/v1/rule/list?category=scoring&status=active

# 测试规则
POST /api/v1/rule/test
{
  "ruleConfig": { /* 规则配置 */ },
  "testData": { /* 测试数据 */ }
}

# 规则版本管理
GET /api/v1/rule/{ruleId}/versions
POST /api/v1/rule/{ruleId}/rollback/{version}
```

## 缓存和热更新机制

### 多级缓存架构
```
L1缓存(内存): 最常用的规则，毫秒级访问
L2缓存(Redis): 分布式缓存，支持集群
L3存储(MySQL): 持久化存储，完整的规则数据
```

### 热更新策略
1. **配置变更检测**：监听数据库变更事件
2. **缓存失效**：精确失效相关规则缓存
3. **预热加载**：后台预加载新规则到缓存
4. **平滑切换**：确保服务不中断的情况下切换规则
5. **回滚机制**：支持快速回滚到上一版本

### 集群同步配置
```json
{
  "cacheSync": {
    "strategy": "event-driven",
    "channels": ["rule-update", "rule-delete"],
    "retryPolicy": {
      "maxRetries": 3,
      "backoffMs": 1000
    }
  }
}
```

## 规则配置界面设计

### 配置方式设计

#### 1. 向导式配置（主推方式）
**适用场景**：新手用户、常见规则场景
**特点**：步骤清晰，降低配置门槛

**配置流程**：
```
步骤1: 选择规则模板
┌─────────────────────────────────────────┐
│ 📊 评分计算类                            │
│ ├─ 项目综合评分                          │
│ ├─ 部门加权评分                          │
│ └─ 多维度评分                            │
│                                        │
│ 💰 奖金分配类                            │
│ ├─ 基础奖金计算                          │
│ ├─ 绩效奖金分配                          │
│ └─ 团队奖金分配                          │
└─────────────────────────────────────────┘

步骤2: 基本信息配置
┌─────────────────────────────────────────┐
│ 规则名称: [项目评分计算规则V2.0]          │
│ 规则描述: [支持多项目的综合评分计算]      │
│ 规则分类: [评分计算] ▼                   │
│ 优先级:   [100] (数字越大优先级越高)      │
│ 版本号:   [2.0]                         │
└─────────────────────────────────────────┘
```

#### 2. 可视化条件构建器
**适用场景**：复杂条件逻辑配置
**特点**：拖拽式操作，直观易懂

```
条件构建器界面:
┌─────────────────────────────────────────────────────────┐
│ 可用字段          │ 条件构建区域                        │
├─────────────────┼─────────────────────────────────────┤
│ 📋 输入参数      │ ┌─ 条件组1 (AND) ─────────────────┐ │
│ • projectScore  │ │ projectScore  [>] [0]    [×]   │ │
│ • departScore   │ │        [AND]                   │ │
│ • coefficient   │ │ departScore   [!=] [null] [×]  │ │
│                 │ └─────────────────────────────────┘ │
│ 🔍 查询参数      │           [OR]                     │
│ • userInfo      │ ┌─ 条件组2 (AND) ─────────────────┐ │
│ • projectData   │ │ userInfo.level [==] ['A'] [×]  │ │
│                 │ └─────────────────────────────────┘ │
│ 🧮 计算参数      │                                    │
│ • avgScore      │ [+ 添加条件组]                      │
│ • totalCount    │                                    │
└─────────────────┴─────────────────────────────────────┘
```

#### 3. 智能表单配置
**适用场景**：标准化规则配置
**特点**：结构化输入，减少错误

```
参数配置表单:
┌─────────────────────────────────────────────────────────┐
│ 📥 输入参数配置                                          │
├─────────────────────────────────────────────────────────┤
│ 参数名称    │ 类型     │ 必填 │ 默认值 │ 描述           │
│ username   │ string   │ ✓   │       │ 用户名         │
│ projectId  │ number   │ ✓   │       │ 项目ID         │
│ coefficient│ number   │     │ 1.0   │ 系数           │
│ [+ 添加参数]                                           │
├─────────────────────────────────────────────────────────┤
│ 🔍 查询参数配置                                          │
├─────────────────────────────────────────────────────────┤
│ 参数名: userScores                                      │
│ SQL查询: [SELECT score FROM user_scores WHERE...]       │
│ 查询参数: [${input.username}, ${input.projectId}]      │
│ 返回类型: [array] ▼                                    │
│ 缓存TTL: [300] 秒                                      │
│ [+ 添加查询]                                           │
└─────────────────────────────────────────────────────────┘
```

#### 4. 代码编辑器模式
**适用场景**：高级用户、复杂规则
**特点**：最大灵活性，支持复杂逻辑

```
Monaco编辑器界面:
┌─────────────────────────────────────────────────────────┐
│ 文件 编辑 查看 帮助                    │ 🔍 搜索 ⚙️ 设置 │
├─────────────────────────────────────────────────────────┤
│  1  {                                                   │
│  2    "ruleId": "advanced_scoring_rule",                │
│  3    "name": "高级评分规则",                            │
│  4    "parameters": {                                   │
│  5      "projectScores": {                              │
│  6        "source": "query",                            │
│  7        "sql": "SELECT * FROM project_scores WHERE...",│
│  8        "type": "array"                               │
│  9      }                                               │
│ 10    },                                                │
│ 11    "conditions": [                                   │
│ 12      {                                               │
│ 13        "when": "projectScores.length > 0",           │
│ 14        "then": {                                     │
│ 15          "formula": "AVG(projectScores) * 1.2"      │
│ 16        }                                             │
│ 17      }                                               │
│ 18    ]                                                 │
│ 19  }                                                   │
├─────────────────────────────────────────────────────────┤
│ 💡 智能提示: 可用参数 projectScores, userInfo...        │
│ ⚠️  语法检查: 第15行 - 建议使用ROUND函数保留小数位       │
└─────────────────────────────────────────────────────────┘

特性:
• 语法高亮
• 自动补全
• 实时语法检查
• 代码折叠
• 多光标编辑
```

### 主界面布局
```
┌─────────────────────────────────────────────────────────┐
│ 🏠 规则引擎管理平台                                      │
├─────────────────────────────────────────────────────────┤
│ 📋 规则列表 │ ➕ 新建 │ 📚 模板 │ 🧪 测试 │ 📊 监控      │
├─────────────┼───────────────────────────────────────────┤
│ 🔍 搜索过滤  │                                          │
│ [搜索框]    │           规则编辑区域                    │
│             │                                          │
│ 📁 分类筛选  │  ┌─ 基本信息 ─┐ ┌─ 参数配置 ─┐          │
│ □ 评分计算   │  │ 规则名称   │ │ 输入参数   │          │
│ □ 奖金分配   │  │ 描述信息   │ │ 查询参数   │          │
│ □ 验证检查   │  │ 版本管理   │ │ 计算参数   │          │
│             │  └───────────┘ └───────────┘          │
│ 📊 状态筛选  │                                          │
│ □ 启用      │  ┌─ 条件设置 ─┐ ┌─ 公式编辑 ─┐          │
│ □ 禁用      │  │ 可视化构建 │ │ 代码编辑器 │          │
│ □ 测试中    │  │ 条件预览   │ │ 语法检查   │          │
│             │  └───────────┘ └───────────┘          │
│ 📋 规则列表  │                                          │
│ • 项目评分V2 │  ┌─ 测试验证 ─┐ ┌─ 版本管理 ─┐          │
│ • 奖金计算V1 │  │ 测试用例   │ │ 历史版本   │          │
│ • 配额检查   │  │ 结果预览   │ │ 变更记录   │          │
│             │  └───────────┘ └───────────┘          │
└─────────────┴───────────────────────────────────────────┘
```

### 实时测试功能
```
┌─────────────────────────────────────────────────────────┐
│ 🧪 规则测试                                              │
├─────────────────────────────────────────────────────────┤
│ 测试数据输入:                                           │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ {                                                  │ │
│ │   "username": "zhangsan",                          │ │
│ │   "projectId": 123,                                │ │
│ │   "coefficient": 1.2                               │ │
│ │ }                                                  │ │
│ └─────────────────────────────────────────────────────┘ │
│ [🚀 执行测试] [📋 使用测试模板] [💾 保存测试用例]        │
│                                                        │
│ 执行结果:                                              │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ ✅ 执行成功                                         │ │
│ │ 📊 最终结果: 102.5                                  │ │
│ │ ⏱️ 执行时间: 15ms                                   │ │
│ │                                                    │ │
│ │ 📋 执行详情:                                        │ │
│ │ 1. 参数获取: ✅ 获取到3个输入参数                    │ │
│ │ 2. 数据查询: ✅ 查询到5条项目评分记录                │ │
│ │ 3. 条件判断: ✅ 匹配条件"双重评分场景"               │ │
│ │ 4. 公式计算: ✅ 85 * 0.4 + 90 * 0.6 = 88           │ │
│ │ 5. 后处理: ✅ 88 * 1.2 = 105.6 → 102.5 (四舍五入)  │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 规则执行引擎详解

### 执行流程
1. **规则加载**：从缓存或数据库加载指定规则配置
2. **参数验证**：验证传入参数的完整性和有效性
3. **参数获取**：按依赖关系获取所有需要的参数
4. **条件评估**：按优先级顺序评估条件分支
5. **公式计算**：执行匹配条件的计算公式
6. **后处理**：执行舍入、格式化等后处理操作
7. **业务规则**：执行验证、约束检查等业务逻辑
8. **结果返回**：返回计算结果和执行详情

### 条件表达式引擎
支持复杂的条件判断语法：
```javascript
// 基础比较
"score > 80"
"status == 'active'"
"department in ['IT', 'Finance']"

// 逻辑组合
"score > 80 && status == 'active'"
"(score > 90) || (score > 80 && bonus == true)"

// 函数调用
"hasRole('manager') && getDepartmentBudget() > 1000"
"isInTimeRange('2024-01-01', '2024-12-31')"

// 数组操作
"projects.length > 0"
"projects.some(p => p.score > 90)"
"scores.avg() > 85"
```

### 公式计算引擎
集成并扩展现有的FormulaCalculator：
```javascript
// 基础数学运算
"score * 0.8 + bonus"
"(projectScore + departmentScore) / 2"

// 函数支持
"SUM(projectScores) / COUNT(projectScores)"
"MAX(scores) * coefficient"
"ROUND(score * 1.2, 2)"

// 条件函数
"IF(score > 90, score * 1.2, score)"
"CASE(score >= 95, 'A', score >= 85, 'B', 'C')"
```

## 与现有系统集成策略

### 渐进式迁移
1. **Phase 1**：新功能使用规则引擎
2. **Phase 2**：简单规则逐步迁移
3. **Phase 3**：复杂业务逻辑迁移
4. **Phase 4**：完全替换硬编码逻辑

### 兼容性保证
- **API兼容**：保持现有API接口不变
- **数据兼容**：现有数据结构无需修改
- **功能兼容**：确保计算结果一致性

### 迁移示例

**现有代码：**
```javascript
// 硬编码的权重计算
const weightedScore = projectScore * 0.4 + departmentScore * 0.6
return Number((coefficient * weightedScore).toFixed(2))
```

**迁移后：**
```javascript
// 调用规则引擎
const result = await calculateByRule({
  ruleId: "weighted_score_calculation",
  parameters: { projectScore, departmentScore, coefficient }
})
```

## 技术实现方案

### 前端技术栈
- **Vue 3 + Composition API**：与现有系统保持一致
- **Element Plus**：UI组件库
- **Monaco Editor**：代码编辑器（VS Code内核）
- **Vue Draggable**：拖拽功能
- **Echarts/D3.js**：规则流程图可视化

### 后端技术栈
- **Go + Gin**：与现有系统保持一致
- **GORM**：数据库ORM
- **Redis**：分布式缓存
- **MySQL**：数据持久化

### 开发优先级
1. **Phase 1**：基础表单配置 + 简单测试（2周）
2. **Phase 2**：可视化条件构建器（3周）
3. **Phase 3**：代码编辑器模式 + 高级功能（2周）
4. **Phase 4**：模板库 + 批量操作（1周）

## 安全和审计机制

### 权限控制
- **角色权限**：不同角色的规则操作权限
- **审批流程**：重要规则变更需要审批
- **操作日志**：完整的操作审计日志

### 数据安全
- **参数验证**：严格的输入参数验证
- **SQL注入防护**：防止恶意规则注入
- **执行沙箱**：规则执行的安全隔离

### 性能优化
- **规则预编译**：将JSON规则编译为执行计划
- **结果缓存**：相同参数的计算结果缓存
- **并发优化**：支持高并发的规则执行
- **资源监控**：实时监控规则执行性能

## 方案优势总结

1. **开发效率高**：业务人员可以直接配置规则，无需程序员参与
2. **维护成本低**：规则变更不需要发布代码，降低风险
3. **响应速度快**：内存缓存机制，毫秒级响应
4. **扩展性强**：支持任意复杂的业务规则
5. **向下兼容**：可以逐步迁移现有逻辑，不影响现有功能
6. **用户体验好**：多种配置方式，适应不同技术水平的用户
7. **安全可靠**：完整的权限控制和审计机制

## 实施建议

1. **先从简单规则开始**：选择1-2个简单的评分规则作为试点
2. **建立规则库**：积累常用的规则模板，提高配置效率
3. **培训用户**：为业务人员提供规则配置培训
4. **监控优化**：持续监控规则执行性能，及时优化
5. **版本管理**：建立规范的规则版本管理流程

这个方案既保持了系统的稳定性，又提供了足够的灵活性来应对未来的业务变化，是一个可行且实用的解决方案。
