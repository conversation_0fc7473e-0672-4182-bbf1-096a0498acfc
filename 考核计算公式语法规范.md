# 考核计算公式语法规范

## 概述

本文档定义了考核系统中计算方法的公式语法规范。该语法是一种领域特定语言（DSL），专门用于考核评分计算场景，结合了数学表达式、SQL聚合函数和Excel公式的特性。

## 基础语法

### 1. 运算符

| 运算符 | 说明 | 示例 |
|--------|------|------|
| `+` | 加法 | `a + b` |
| `-` | 减法 | `a - b` |
| `*` | 乘法 | `a * b` |
| `/` | 除法 | `a / b` |
| `^` 或 `**` | 幂运算 | `a ^ 2` 或 `a ** 2` |
| `()` | 括号，改变运算优先级 | `(a + b) * c` |

### 2. 比较运算符

| 运算符 | 说明 | 示例 |
|--------|------|------|
| `>` | 大于 | `score > 80` |
| `<` | 小于 | `score < 60` |
| `>=` | 大于等于 | `score >= 80` |
| `<=` | 小于等于 | `score <= 100` |
| `==` | 等于 | `status == 1` |
| `!=` | 不等于 | `status != 0` |

## 函数库

### 1. 聚合函数

#### SUM(参数1, 参数2, ...)
**功能：** 计算所有参数的总和
**语法：** `SUM(param1, param2, param3, ...)`
**示例：**
```
SUM(project_weight, score_threshold, bonus_ratio)
SUM(member1_score, member2_score, member3_score)
SUM(base_score * 0.6, bonus_score * 0.4)
```

#### AVG(参数1, 参数2, ...)
**功能：** 计算所有参数的平均值
**语法：** `AVG(param1, param2, param3, ...)`
**示例：**
```
AVG(project_weight, score_threshold) * 100
AVG(quality_score, efficiency_score, collaboration_score)
```

#### MAX(参数1, 参数2, ...)
**功能：** 返回所有参数中的最大值
**语法：** `MAX(param1, param2, param3, ...)`
**示例：**
```
MAX(individual_score, team_score)
MAX(base_score, adjusted_score, bonus_score)
```

#### MIN(参数1, 参数2, ...)
**功能：** 返回所有参数中的最小值
**语法：** `MIN(param1, param2, param3, ...)`
**示例：**
```
MIN(target_score, actual_score)
MIN(score1, score2, score3)
```

#### COUNT(参数1, 参数2, ...)
**功能：** 计算非空参数的数量
**语法：** `COUNT(param1, param2, param3, ...)`
**示例：**
```
COUNT(completed_tasks, pending_tasks, cancelled_tasks)
```

### 2. 数学函数

#### SQRT(参数)
**功能：** 计算平方根
**语法：** `SQRT(number)`
**示例：**
```
SQRT(variance_score)
SQRT(SUM(x^2, y^2, z^2))
```

#### ABS(参数)
**功能：** 计算绝对值
**语法：** `ABS(number)`
**示例：**
```
ABS(actual_score - target_score)
ABS(deviation)
```

#### ROUND(参数, 小数位数)
**功能：** 四舍五入到指定小数位数
**语法：** `ROUND(number, digits)`
**示例：**
```
ROUND(final_score, 2)
ROUND(AVG(scores), 1)
```

### 3. 条件函数

#### IF(条件, 真值, 假值)
**功能：** 根据条件返回不同的值
**语法：** `IF(condition, value_if_true, value_if_false)`
**示例：**
```
IF(score_threshold > 80, project_weight * 1.2, project_weight * 1.0)
IF(performance_level == "excellent", bonus_ratio * 1.5, bonus_ratio)
```

#### CASE(条件1, 值1, 条件2, 值2, ..., 默认值)
**功能：** 多条件判断
**语法：** `CASE(condition1, value1, condition2, value2, default_value)`
**示例：**
```
CASE(
  score >= 90, "优秀",
  score >= 80, "良好",
  score >= 70, "中等",
  "需改进"
)
```

## 参数引用

### 参数命名规则
- 参数名必须为英文字母、数字和下划线组成
- 参数名不能以数字开头
- 参数名区分大小写
- 建议使用下划线分隔的小写命名方式

### 参数使用示例
```
project_weight          // 项目权重系数
score_threshold         // 评分阈值设置
bonus_ratio            // 奖金分配比例
assessment_cycle       // 考核周期配置
```

## 复杂公式示例

### 1. 加权平均计算
```
SUM(project_weight * 0.4, score_threshold * 0.3, bonus_ratio * 0.3)
```

### 2. 条件加权计算
```
IF(score_threshold > 80, 
   SUM(project_weight * 1.2, bonus_ratio * 0.8), 
   SUM(project_weight * 1.0, bonus_ratio * 1.0))
```

### 3. 多层嵌套计算
```
ROUND(
  MAX(project_weight, score_threshold) + 
  AVG(bonus_ratio, assessment_cycle), 
  2
)
```

### 4. 综合评分公式
```
SUM(
  AVG(individual_scores) * 0.4,
  team_collaboration_score * 0.3,
  leadership_score * 0.2,
  innovation_score * 0.1
) * performance_multiplier
```

### 5. 业务场景公式
```
// 项目考核综合评分
SUM(
  项目成员精力分配 * 对应的项目评价 + 
  项目负责人精力分配 * 机构负责人评价
) * 0.4 + 机构负责人评价 * 0.6
```

## 语法规则

### 1. 优先级规则
1. 括号 `()`
2. 函数调用
3. 幂运算 `^` `**`
4. 乘除法 `*` `/`
5. 加减法 `+` `-`
6. 比较运算符 `>` `<` `>=` `<=` `==` `!=`

### 2. 函数嵌套
- 支持函数内调用其他函数
- 嵌套层数建议不超过3层，保持可读性

### 3. 数据类型
- **数值型：** 整数和浮点数
- **布尔型：** true/false（用于条件判断）
- **字符串：** 用双引号包围（主要用于CASE函数返回值）

### 4. 注释
```
// 这是单行注释
/* 这是多行注释 */
```

## 错误处理

### 常见错误类型
1. **语法错误：** 括号不匹配、运算符使用错误
2. **参数错误：** 引用不存在的参数
3. **函数错误：** 函数名拼写错误、参数数量不匹配
4. **类型错误：** 数据类型不匹配

### 调试建议
1. 使用括号明确运算优先级
2. 分步骤构建复杂公式
3. 验证参数名称的正确性
4. 测试边界条件和异常情况

## 最佳实践

### 1. 可读性
- 使用有意义的参数名
- 适当使用括号和空格
- 复杂公式分行书写

### 2. 性能
- 避免过度嵌套
- 合理使用聚合函数
- 考虑计算复杂度

### 3. 维护性
- 添加必要的注释
- 模块化复杂逻辑
- 定期review和优化

## 版本信息

- **版本：** 1.0
- **更新日期：** 2024年
- **适用系统：** 考核管理系统
- **维护者：** 系统开发团队
