# 用户参数评分校验功能说明

## 功能概述

在 `/assessmentData/getUserParameterScores` 接口中新增了评分完整性校验功能，确保用户在获取评分数据前，相关的评分数据已经完整。

## 校验逻辑

### 触发条件

只有当用户的计算参数**同时包含**以下两个参数时，才会触发校验：
- `project_participation` (项目参与度)
- `project_manager_score` (项目经理评分)

### 校验步骤

#### 1. 考核系数分配检查
```sql
-- 检查用户在指定考核配置中是否有考核系数分配
SELECT COUNT(*) FROM assessment_coefficient_allocation 
WHERE username = ? AND assessment_config_id = ?
```

**校验结果：**
- 如果 `count = 0`：返回错误 `"该名用户需要考核系数分配，请先进行分配"`
- 如果 `count > 0`：继续下一步校验

#### 2. 项目负责人评分检查
```sql
-- 获取用户的所有考核系数分配记录
SELECT * FROM assessment_coefficient_allocation 
WHERE username = ? AND assessment_config_id = ?

-- 对每个项目检查是否有项目负责人评分
SELECT COUNT(*) FROM project_manager_score 
WHERE coefficient_allocation_id = ?
```

**校验结果：**
- 如果某个项目没有评分：收集项目信息，格式化为 `"xxx项目未评分-项目负责人xxx"`
- 如果所有项目都有评分：继续下一步校验

#### 3. 部门经理评分检查（可选）
```sql
-- 如果用户的计算参数包含 department_manager_score
SELECT COUNT(*) FROM department_manager_score 
WHERE username = ? AND assessment_config_id = ?
```

**校验结果：**
- 如果 `count = 0`：返回错误 `"机构负责人暂无评分"`
- 如果 `count > 0`：校验通过

## 错误信息格式

### 1. 缺少考核系数分配
```json
{
  "code": 500,
  "msg": "获取用户参数评分失败: 该名用户需要考核系数分配，请先进行分配"
}
```

### 2. 项目负责人未评分
```json
{
  "code": 500,
  "msg": "获取用户参数评分失败: 测试项目未评分-项目负责人张三，开发项目未评分-项目负责人李四"
}
```

### 3. 部门经理未评分
```json
{
  "code": 500,
  "msg": "获取用户参数评分失败: 机构负责人暂无评分"
}
```

## 项目负责人识别逻辑

从 `project_info` 表中获取项目负责人信息：

1. **manager_id** - 项目经理用户名（从project_info表获取）
2. 通过 **manager_id** 从 `sys_users` 表获取用户昵称作为显示名称
3. 如果找不到用户昵称，则使用用户名
4. 如果 **manager_id** 为空，则显示 **"未知"**

```sql
-- 获取项目基本信息
SELECT name, manager_id
FROM project_info
WHERE id = ?

-- 获取项目负责人昵称
SELECT nick_name
FROM sys_users
WHERE username = ?
```

## 代码实现

### 主要函数

#### `validateUserScoreCompleteness`
- 校验单个用户在指定考核配置下的评分完整性
- 参数：`userName`, `configId`, `hasDepartmentManagerScore`
- 返回：错误信息或 `nil`

#### `getProjectInfoAndManager`
- 获取项目名称和项目负责人信息
- 参数：`projectId`
- 返回：`projectName`, `managerName`, `error`

### 集成位置

在 `GetUserParameterScores` 函数中，在查询评分数据之前进行校验：

```go
// 步骤3: 对每个用户进行评分完整性校验
for _, userName := range req.UserNames {
    calcMethod := calcMethodMap[userName]
    if calcMethod != nil {
        // 检查计算参数
        if hasProjectParticipation && hasProjectManagerScore {
            // 执行校验
            err := s.validateUserScoreCompleteness(ctx, userName, configId, hasDepartmentManagerScore)
            if err != nil {
                return nil, err
            }
        }
    }
}
```

## 性能考虑

### 查询优化
1. 使用 `COUNT(*)` 而不是 `SELECT *` 来检查记录存在性
2. 批量处理多个用户的校验
3. 只对需要校验的用户执行校验逻辑

### 数据库索引建议
```sql
-- 考核系数分配表索引
CREATE INDEX idx_aca_user_config ON assessment_coefficient_allocation(username, assessment_config_id);

-- 项目负责人评分表索引  
CREATE INDEX idx_pms_coefficient ON project_manager_score(coefficient_allocation_id);

-- 部门经理评分表索引
CREATE INDEX idx_dms_user_config ON department_manager_score(username, assessment_config_id);
```

## 测试场景

### 1. 正常场景
- 用户有考核系数分配
- 所有项目都有负责人评分
- 部门经理已评分（如果需要）
- **预期结果：** 校验通过，正常返回评分数据

### 2. 缺少考核系数分配
- 用户在指定考核配置中没有考核系数分配
- **预期结果：** 返回错误 "该名用户需要考核系数分配，请先进行分配"

### 3. 项目负责人未评分
- 用户有考核系数分配
- 但某些项目的负责人还没有进行评分
- **预期结果：** 返回错误列出未评分的项目和负责人

### 4. 部门经理未评分
- 用户需要部门经理评分但还没有评分记录
- **预期结果：** 返回错误 "机构负责人暂无评分"

### 5. 不触发校验
- 用户的计算参数不包含 `project_participation` 或 `project_manager_score`
- **预期结果：** 跳过校验，直接返回评分数据

## 部署注意事项

1. **数据库兼容性：** 确保相关表结构已正确创建
2. **索引优化：** 建议添加上述推荐的数据库索引
3. **错误处理：** 前端需要适配新的错误信息格式
4. **性能监控：** 监控校验逻辑对接口响应时间的影响

## 后续优化建议

1. **缓存机制：** 对项目信息和用户计算方法进行缓存
2. **批量校验：** 优化为批量查询减少数据库访问次数
3. **异步校验：** 考虑将校验逻辑异步化，提高响应速度
4. **配置化：** 将校验规则配置化，便于后续调整
