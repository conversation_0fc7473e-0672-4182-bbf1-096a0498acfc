<template>
  <div>
    <div class="flex gap-4 p-2">
      <div
        class="flex-none w-52 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded p-4"
      >
        <div class="flex justify-between items-center">
          <span class="text font-bold">考核类别</span>
          <el-button type="primary" @click="openDrawer"> 新增 </el-button>
        </div>
        <el-scrollbar class="mt-4" style="height: calc(100vh - 300px)">
          <div v-if="loading" class="text-center py-4">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span class="ml-2">加载中...</span>
          </div>
          <div v-else-if="menuItems.length === 0" class="text-center py-4 text-gray-500">
            暂无数据
          </div>
          <div
            v-else
            v-for="item in menuItems"
            :key="item.id"
            class="rounded flex justify-between items-center px-2 py-4 cursor-pointer mt-2 hover:bg-blue-50 dark:hover:bg-blue-900 bg-gray-50 dark:bg-gray-800 gap-4"
            :class="
              selectID === item.id
                ? 'text-active'
                : 'text-slate-700 dark:text-slate-50'
            "
            @click="toDetail(item)"
          >
            <span class="max-w-[160px] truncate">{{ item.categoryName }}</span>
            <div class="min-w-[40px]">
              <el-icon
                class="text-blue-500"
                @click.stop="updateItem(item)"
              >
                <Edit />
              </el-icon>
              <el-icon
                class="ml-2 text-red-500"
                @click="deleteItem(item)"
              >
                <Delete />
              </el-icon>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div
        class="flex-1 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded p-4 flex flex-col"
      >
        <!-- 上半部分内容区域 - 参数设置 -->
        <div class="flex-1 flex flex-col">
          <div class="mb-4">
            <h3 class="text-lg font-bold mb-4">参数设置</h3>

            <!-- 操作按钮区域 -->
            <div class="flex justify-start items-center mb-4">
              <div class="flex gap-2">
                <el-button type="primary" @click="openParameterDrawer">新增</el-button>
                <el-button
                  type="danger"
                  :disabled="!selectedParameters.length"
                  @click="batchDeleteParameters"
                >
                  删除
                </el-button>
              </div>
            </div>

            <!-- 参数表格 -->
            <el-table
              :data="parameterData"
              style="width: 100%"
              @selection-change="handleSelectionChange"
              max-height="300"
              :cell-style="{ padding: '4px 0' }"
              v-loading="parameterLoading"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="parameterName" label="参数名称" min-width="1" />
              <el-table-column prop="roleName" label="关联角色" min-width="1">
                <template #default="scope">
                  <div v-if="scope.row.roleNames && scope.row.roleNames.length > 0">
                    <el-tag
                      v-for="(roleName, index) in scope.row.roleNames"
                      :key="index"
                      :type="getRoleTagType(scope.row.roleIds ? scope.row.roleIds[index] : null)"
                      style="margin-right: 4px; margin-bottom: 2px;"
                    >
                      {{ roleName }}
                    </el-tag>
                  </div>
                  <el-tag v-else-if="scope.row.roleName" :type="getRoleTagType(scope.row.roleId)">
                    {{ scope.row.roleName }}
                  </el-tag>
                  <el-tag v-else type="info">
                    未分配角色
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="parameterNameCn" label="中文名称" min-width="1" />
              <el-table-column label="操作" min-width="1">
                <template #default="scope">
                  <el-button
                    type="primary"
                    size="small"
                    @click="editParameter(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteParameter(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 分隔线 -->
        <el-divider class="my-1" />

        <!-- 下半部分内容区域 - 计算方法 -->
        <div class="flex-1 flex flex-col">
          <div class="mb-4">
            <h3 class="text-lg font-bold mb-4">计算方法</h3>

            <!-- 操作按钮区域 -->
            <div class="flex justify-start items-center mb-4">
              <div class="flex gap-2">
                <el-button type="primary" @click="openMethodDrawer">新增</el-button>
                <el-button
                  type="danger"
                  :disabled="!selectedMethods.length"
                  @click="batchDeleteMethods"
                >
                  删除
                </el-button>
              </div>
            </div>

            <!-- 计算方法表格 -->
            <el-table
              :data="methodData"
              style="width: 100%"
              @selection-change="handleMethodSelectionChange"
              max-height="300"
              :cell-style="{ padding: '4px 0' }"
              v-loading="methodLoading"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="methodName" label="计算方法名称" min-width="1" />
              <el-table-column prop="description" label="方法描述" min-width="1" />
              <el-table-column label="操作" min-width="1">
                <template #default="scope">
                  <el-button
                    type="primary"
                    size="small"
                    @click="editMethod(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteMethod(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <el-drawer
      v-model="drawerFormVisible"
      :size="appStore.drawerSize"
      :show-close="false"
      :before-close="closeDrawer"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{
            type === 'create' ? '添加考核类别' : '修改考核类别'
          }}</span>
          <div>
            <el-button @click="closeDrawer"> 取 消 </el-button>
            <el-button type="primary" @click="enterDrawer"> 确 定 </el-button>
          </div>
        </div>
      </template>
      <el-form
        ref="drawerForm"
        :model="formData"
        :rules="rules"
        label-width="110px"
      >
        <el-form-item label="类别名称" prop="categoryName">
          <el-input
            v-model="formData.categoryName"
            placeholder="请输入类别名称"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
        <el-form-item label="类别描述" prop="description">
          <el-input
            v-model="formData.description"
            placeholder="请输入类别描述"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number
            v-model="formData.sortOrder"
            :min="0"
            placeholder="请输入排序值"
            :style="{ width: '100%' }"
          />
        </el-form-item>
      </el-form>
    </el-drawer>

    <!-- 参数设置抽屉 -->
    <el-drawer
      v-model="parameterDrawerVisible"
      :size="appStore.drawerSize"
      :show-close="false"
      :before-close="closeParameterDrawer"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{
            parameterType === 'create' ? '添加参数' : '修改参数'
          }}</span>
          <div>
            <el-button @click="closeParameterDrawer"> 取 消 </el-button>
            <el-button type="primary" @click="submitParameter"> 确 定 </el-button>
          </div>
        </div>
      </template>
      <el-form
        ref="parameterForm"
        :model="parameterFormData"
        :rules="parameterRules"
        label-width="110px"
      >
        <el-form-item label="参数名称" prop="parameterName">
          <el-input
            v-model="parameterFormData.parameterName"
            placeholder="请输入英文参数名称"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
        <el-form-item label="关联角色" prop="roleIds">
          <el-select
            v-model="parameterFormData.roleIds"
            placeholder="请选择关联角色（可多选）"
            multiple
            clearable
            :loading="authorityLoading"
            :style="{ width: '100%' }"
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option
              v-for="authority in authorityList"
              :key="authority.authorityId"
              :label="authority.authorityName"
              :value="authority.authorityId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="中文名称" prop="parameterNameCn">
          <el-input
            v-model="parameterFormData.parameterNameCn"
            placeholder="请输入中文名称"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
      </el-form>
    </el-drawer>

    <!-- 计算方法抽屉 -->
    <el-drawer
      v-model="methodDrawerVisible"
      :size="appStore.drawerSize"
      :show-close="false"
      :before-close="closeMethodDrawer"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{
            methodType === 'create' ? '添加计算方法' : '修改计算方法'
          }}</span>
          <div>
            <el-button @click="closeMethodDrawer"> 取 消 </el-button>
            <el-button type="primary" @click="submitMethod"> 确 定 </el-button>
          </div>
        </div>
      </template>
      <el-form
        ref="methodForm"
        :model="methodFormData"
        :rules="methodRules"
        label-width="110px"
      >
        <el-form-item label="方法名称" prop="methodName">
          <el-input
            v-model="methodFormData.methodName"
            placeholder="请输入计算方法名称"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
        <el-form-item label="方法描述" prop="description">
          <el-input
            v-model="methodFormData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入方法描述"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>

        <!-- 参数选择 -->
        <el-form-item label="参数配置">
          <div class="space-y-2">

            <div
              v-for="param in parameterData"
              :key="param.id"
              class="flex items-center"
            >
              <el-checkbox
                v-model="methodFormData.selectedParameters"
                :label="param.id"
                @change="updateAvailableParameters"
              >
                是否需要{{ param.parameterNameCn || param.parameterName || '未命名参数' }}
              </el-checkbox>
            </div>
          </div>
        </el-form-item>

        <!-- 可用参数显示 -->
        <el-form-item label="可用参数" v-if="availableParameters.length > 0">
          <div class="text-sm text-gray-600 bg-gray-50 p-2 rounded">
            {{ availableParameters.join(', ') }}
          </div>
        </el-form-item>

        <!-- 规则引擎配置 -->
        <div class="rule-engine-config">
          <!-- 调试信息 -->
          <div v-if="false" class="debug-info">
            <el-alert title="调试信息" type="info" :closable="false">
              <p><strong>配置模式:</strong> {{ methodFormData.configMode }}</p>
              <p><strong>规则类型:</strong> {{ methodFormData.ruleType }}</p>
              <p><strong>输入参数数量:</strong> {{ methodFormData.ruleConfig.inputParameters?.length || 0 }}</p>
              <p><strong>条件数量:</strong> {{ methodFormData.ruleConfig.conditions?.length || 0 }}</p>
              <p><strong>默认值:</strong> {{ methodFormData.ruleConfig.defaultValue }}</p>
              <p><strong>原始formula长度:</strong> {{ methodFormData.formula?.length || 0 }}</p>
            </el-alert>
          </div>

          <!-- 输入参数配置 -->
          <el-form-item label="输入参数">
            <RuleParameterConfig
              v-model="methodFormData.ruleConfig.inputParameters"
              :available-parameters="parameterData"
            />
          </el-form-item>

          <!-- 条件配置 -->
          <el-form-item label="规则条件">
            <RuleConditionBuilder
              v-model="methodFormData.ruleConfig.conditions"
              :parameters="methodFormData.ruleConfig.inputParameters"
              :default-value="methodFormData.ruleConfig.defaultValue"
              @update:defaultValue="updateDefaultValue"
            />
          </el-form-item>

          <!-- 规则测试 -->
          <el-form-item label="规则测试">
            <RuleTestPanel
              :rule-config="methodFormData.ruleConfig"
              @test-result="handleTestResult"
            />
          </el-form-item>
        </div>

        <!-- 分配人员 -->
        <el-form-item label="分配人员">
          <div v-loading="userLoading" style="min-height: 300px;">
            <el-transfer
              v-model="methodFormData.assignedUsers"
              :data="availableUsers"
              :titles="['可选用户', '已分配用户']"
              :button-texts="['移除', '添加']"
              :format="{
                noChecked: '${total}',
                hasChecked: '${checked}/${total}'
              }"
              filterable
              filter-placeholder="搜索用户"
              style="text-align: left; display: inline-block"
            />
          </div>
          <div class="text-xs text-gray-500 mt-2">
            注意：一个用户只能对应一个考核方法，已被其他考核方法分配的用户将显示为禁用状态
          </div>
        </el-form-item>
      </el-form>
    </el-drawer>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Edit, Delete, Loading } from '@element-plus/icons-vue'
  import { useAppStore } from '@/pinia'
  import {
    createAssessmentCategories,
    deleteAssessmentCategories,
    updateAssessmentCategories,
    getAssessmentCategoriesList
  } from '@/api/assessment/assessmentCategories'
  import { getAuthorityList } from '@/api/authority'
  import { getUserList } from '@/api/user'
  // 导入组织相关API
  import {
    getUserLoginList,
    getOrganizationalMember,
    getOrganizationalTree,
    getUser
  } from '@/plugin/organizational/api/organizational'
  import {
    createCalculationParameters,
    deleteCalculationParameters,
    deleteCalculationParametersByIds,
    updateCalculationParameters,
    getCalculationParametersWithRoleNameList
  } from '@/api/assessment/calculationParameters'
  import {
    createCalculationMethods,
    deleteCalculationMethods,
    deleteCalculationMethodsByIds,
    updateCalculationMethods,
    findCalculationMethods,
    getCalculationMethodsList
  } from '@/api/assessment/calculationMethods'

  // 导入规则引擎组件
  import RuleParameterConfig from '@/components/rule/RuleParameterConfig.vue'
  import RuleConditionBuilder from '@/components/rule/RuleConditionBuilder.vue'
  import RuleTestPanel from '@/components/rule/RuleTestPanel.vue'

  defineOptions({
    name: 'Dashboard'
  })

  const appStore = useAppStore()

  const selectID = ref(0)

  const formData = ref({
    categoryName: null,
    description: null,
    sortOrder: 0
  })

  const rules = ref({
    categoryName: [
      {
        required: true,
        message: '请输入类别名称',
        trigger: 'blur'
      }
    ]
  })

  // 考核类别数据
  const menuItems = ref([])
  const loading = ref(false)

  // 参数设置相关数据
  const parameterData = ref([])
  const selectedParameters = ref([])
  const parameterLoading = ref(false)

  // 角色数据（仅用于表单选择）
  const authorityList = ref([])
  const authorityLoading = ref(false)

  const parameterDrawerVisible = ref(false)
  const parameterType = ref('')
  const parameterFormData = ref({
    parameterName: '',
    roleId: null, // 保留原字段用于后端兼容
    roleIds: [], // 新增多选字段
    parameterNameCn: '',
    assessmentCategoryId: null
  })

  const parameterRules = ref({
    parameterName: [
      {
        required: true,
        message: '请输入参数名称',
        trigger: 'blur'
      },
      {
        pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
        message: '参数名称必须为英文，只能包含字母、数字和下划线，且不能以数字开头',
        trigger: 'blur'
      }
    ]
    // 移除了 roleId 的必填验证，关联角色改为可选
  })

  // 查询考核类别数据
  const getTableData = async () => {
    loading.value = true
    try {
      const res = await getAssessmentCategoriesList({
        page: 1,
        pageSize: 999 // 获取所有数据
      })
      if (res.code === 0) {
        menuItems.value = res.data.list || []
        if (menuItems.value.length > 0) {
          selectID.value = menuItems.value[0].id
          // 设置默认选中后，加载对应的参数和方法
          getParameterListData()
          getMethodListData()
        }
      }
    } catch (error) {
      console.error('获取考核类别失败:', error)
      ElMessage.error('获取考核类别失败')
    } finally {
      loading.value = false
    }
  }

  getTableData()

  // 获取角色列表（仅用于表单选择）
  const getAuthorityListData = async () => {
    authorityLoading.value = true
    try {
      const res = await getAuthorityList()
      if (res.code === 0) {
        authorityList.value = res.data || []
        // 角色列表加载完成后，重新加载参数列表以正确映射角色名称
        if (selectID.value) {
          getParameterListData()
        }
      }
    } catch (error) {
      console.error('获取角色列表失败:', error)
      ElMessage.error('获取角色列表失败')
    } finally {
      authorityLoading.value = false
    }
  }

  getAuthorityListData()

  // 获取参数列表
  const getParameterListData = async () => {
    parameterLoading.value = true
    try {
      const res = await getCalculationParametersWithRoleNameList({
        page: 1,
        pageSize: 999 // 获取所有数据
      })
      if (res.code === 0) {
        const allParameters = res.data.list || []

        // 处理多角色数据
        const processedParameters = allParameters.map(param => {
          const processed = { ...param }

          // 如果roleId是逗号分隔的字符串，处理为多角色
          if (param.roleId && typeof param.roleId === 'string' && param.roleId.includes(',')) {
            const roleIds = param.roleId.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
            processed.roleIds = roleIds

            // 根据roleIds获取对应的角色名称
            const roleNames = roleIds.map(roleId => {
              const authority = authorityList.value.find(auth => auth.authorityId === roleId)
              return authority ? authority.authorityName : `角色${roleId}`
            })
            processed.roleNames = roleNames
          } else if (param.roleId) {
            // 单个角色的情况
            processed.roleIds = [param.roleId]
            processed.roleNames = param.roleName ? [param.roleName] : [`角色${param.roleId}`]
          }

          return processed
        })

        // 根据当前选中的考核类别筛选参数
        if (selectID.value) {
          parameterData.value = processedParameters.filter(param =>
            param.assessmentCategoryId === selectID.value
          )
        } else {
          parameterData.value = processedParameters
        }
      }
    } catch (error) {
      console.error('获取参数列表失败:', error)
      ElMessage.error('获取参数列表失败')
    } finally {
      parameterLoading.value = false
    }
  }

  // 获取计算方法列表
  const getMethodListData = async () => {
    methodLoading.value = true
    try {
      const res = await getCalculationMethodsList({
        page: 1,
        pageSize: 999 // 获取所有数据
      })
      if (res.code === 0) {
        const basicMethods = res.data.list || []

        // 为每个方法获取完整的详细信息（包括用户分配）
        const detailedMethods = []
        for (const method of basicMethods) {
          try {
            const detailRes = await findCalculationMethods({ id: method.id })
            if (detailRes.code === 0) {
              detailedMethods.push({
                ...method,
                assignedUsers: detailRes.data.assignedUsers || []
              })
            } else {
              // 如果获取详细信息失败，使用基本信息
              detailedMethods.push({
                ...method,
                assignedUsers: []
              })
            }
          } catch (error) {
            console.error(`获取方法${method.id}详细信息失败:`, error)
            // 如果获取详细信息失败，使用基本信息
            detailedMethods.push({
              ...method,
              assignedUsers: []
            })
          }
        }

        // 存储所有计算方法数据（用于用户分配检查）
        allMethodData.value = detailedMethods
        // 根据当前选中的考核类别筛选计算方法
        if (selectID.value) {
          methodData.value = detailedMethods.filter(method =>
            method.assessmentCategoryId === selectID.value
          )
        } else {
          methodData.value = detailedMethods
        }
      }
    } catch (error) {
      console.error('获取计算方法列表失败:', error)
      ElMessage.error('获取计算方法列表失败')
    } finally {
      methodLoading.value = false
    }
  }

  // 获取权限范围内的成员
  const getMembersInScope = async () => {
    try {
      // console.log('开始获取权限范围内的成员...')

      // 获取用户的登录节点权限
      const userLoginRes = await getUserLoginList()
      // console.log('用户登录权限响应:', userLoginRes)

      if (userLoginRes.code !== 0 || !userLoginRes.data) {
        console.warn('获取用户登录权限失败，尝试获取所有用户')
        return await getAllUsers()
      }

      // 去重组织ID
      const orgIds = [...new Set(userLoginRes.data.map(item => item.org_id))]
      // console.log('用户有权限的组织ID列表:', orgIds)

      if (orgIds.length === 0) {
        console.warn('用户没有任何组织权限，获取所有用户')
        return await getAllUsers()
      }

      // 并发获取所有组织的成员，使用正确的参数名 ID
      const memberPromises = orgIds.map(orgId =>
        getOrganizationalMember({
          ID: orgId,  // 使用 ID 而不是 orgId
          page: 1,
          pageSize: 100  // 增加页面大小以获取更多成员
        }).catch(error => {
          console.error(`获取组织${orgId}成员失败:`, error)
          return { code: -1, data: { list: [] } }
        })
      )

      const memberResults = await Promise.all(memberPromises)
      // console.log('所有组织成员API响应:', memberResults)

      // 合并所有成员并去重
      const allMembers = []
      const userIdSet = new Set()

      memberResults.forEach((result, index) => {
        // console.log(`组织${orgIds[index]}的成员API响应:`, result)

        if (result.code === 0 && result.data && result.data.list) {
          const memberList = result.data.list
          // console.log(`组织${orgIds[index]}的成员列表:`, memberList)

          memberList.forEach(member => {
            const userId = member.user_id || member.User?.ID
            const user = member.User || member.user

            if (userId && !userIdSet.has(userId)) {
              userIdSet.add(userId)

              // 获取组织名称，优先使用关联的组织信息
              let orgName = '未知组织'
              if (member.Organizational?.name) {
                orgName = member.Organizational.name
              } else if (member.organizational?.name) {
                orgName = member.organizational.name
              } else if (member.orgName) {
                orgName = member.orgName
              } else {
                // 如果没有组织名称，尝试从组织树中查找
                orgName = getOrgNameById(orgIds[index]) || `组织${orgIds[index]}`
              }

              // 构建用户显示名称
              const userName = user?.nickName || user?.userName || `用户${userId}`

              allMembers.push({
                key: userId,
                label: `${userName} (${orgName})`,
                disabled: false,
                userId: userId,
                userName: user?.userName,
                nickName: user?.nickName,
                orgId: member.org_id || orgIds[index],
                orgName: orgName
              })
            }
          })
        }
      })

      // console.log('权限范围内的最终成员列表:', allMembers)

      // 如果权限范围内没有成员，回退到获取所有用户
      if (allMembers.length === 0) {
        // console.log('权限范围内没有成员，回退到获取所有用户')
        return await getAllUsers()
      }

      return allMembers

    } catch (error) {
      console.error('获取权限范围内成员失败:', error)
      // 出错时回退到获取所有用户
      return await getAllUsers()
    }
  }

  // 回退方案：获取所有用户
  const getAllUsers = async () => {
    try {
      // console.log('获取所有用户列表...')
      const userRes = await getUser()
      // console.log('所有用户API响应:', userRes)

      if (userRes.code !== 0 || !userRes.data) {
        console.warn('获取所有用户失败，尝试系统用户接口:', userRes)
        // 如果组织用户接口失败，尝试系统用户接口
        const systemUserRes = await getUserList({
          page: 1,
          pageSize: 999,
          username: '',
          nickName: '',
          phone: '',
          email: ''
        })

        if (systemUserRes.code === 0) {
          return (systemUserRes.data.list || []).map(user => ({
            key: user.ID,
            label: user.nickName || user.userName,
            disabled: false,
            userId: user.ID,
            userName: user.userName,
            nickName: user.nickName,
            orgId: null,
            orgName: '系统用户'
          }))
        }
        return []
      }

      const userList = Array.isArray(userRes.data) ? userRes.data : []
      // console.log('解析到的用户列表:', userList)

      return userList.map(user => ({
        key: user.ID || user.id,
        label: `${user.nickName || user.userName || `用户${user.ID}`}`,
        disabled: false,
        userId: user.ID || user.id,
        userName: user.userName,
        nickName: user.nickName,
        orgId: null,
        orgName: '系统用户'
      })).filter(member => member.userId)

    } catch (error) {
      console.error('获取所有用户失败:', error)
      return []
    }
  }

  // 获取用户列表
  const getUserListData = async () => {
    userLoading.value = true
    try {
      const members = await getMembersInScope()
      allUsers.value = members
      // console.log('可访问用户:', allUsers.value)
    } catch (error) {
      console.error('加载用户失败:', error)
      ElMessage.error('获取用户列表失败')
    } finally {
      userLoading.value = false
    }
  }

  // 加载组织树并构建组织ID到名称的映射
  const loadOrganizationTree = async () => {
    try {
      const treeRes = await getOrganizationalTree()
      if (treeRes.code === 0 && treeRes.data) {
        organizationTree.value = treeRes.data

        // 递归构建组织ID到名称的映射
        const buildOrgMap = (orgs) => {
          if (Array.isArray(orgs)) {
            orgs.forEach(org => {
              const id = org.ID || org.id
              const name = org.name || org.Name
              if (id && name) {
                organizationMap.value.set(id, name)
              }
              // 递归处理子组织
              const children = org.children || org.Children
              if (children && children.length > 0) {
                buildOrgMap(children)
              }
            })
          }
        }

        buildOrgMap(treeRes.data)
        // console.log('组织映射表:', organizationMap.value)
      }
    } catch (error) {
      console.error('加载组织树失败:', error)
    }
  }

  // 根据组织ID获取组织名称
  const getOrgNameById = (orgId) => {
    return organizationMap.value.get(orgId) || null
  }

  const toDetail = (row) => {
    selectID.value = row.id
    // 切换考核类别时，重新加载该类别下的参数和方法
    getParameterListData()
    getMethodListData()
  }

  const drawerFormVisible = ref(false)
  const type = ref('')

  const updateItem = async (row) => {
    type.value = 'update'
    formData.value = { ...row }
    drawerFormVisible.value = true
  }

  const closeDrawer = () => {
    drawerFormVisible.value = false
    formData.value = {
      categoryName: null,
      description: null,
      sortOrder: 0
    }
  }

  const deleteItem = async (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const res = await deleteAssessmentCategories({ id: row.id })
        if (res.code === 0) {
          ElMessage.success('删除成功')
          getTableData()
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    })
  }

  const drawerForm = ref(null)
  const enterDrawer = async () => {
    drawerForm.value.validate(async (valid) => {
      if (!valid) return

      try {
        let res
        if (type.value === 'create') {
          res = await createAssessmentCategories(formData.value)
        } else {
          res = await updateAssessmentCategories(formData.value)
        }

        if (res.code === 0) {
          ElMessage.success('操作成功')
          closeDrawer()
          getTableData()
        } else {
          ElMessage.error(res.msg || '操作失败')
        }
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    })
  }

  const openDrawer = () => {
    type.value = 'create'
    drawerForm.value && drawerForm.value.clearValidate()
    drawerFormVisible.value = true
  }

  // 参数设置相关方法
  const handleSelectionChange = (selection) => {
    selectedParameters.value = selection
  }

  const getRoleTagType = (roleId) => {
    // 根据角色ID返回不同的标签类型
    if (!roleId) return 'info'

    // 可以根据角色ID设置不同颜色
    const typeMap = {
      888: 'danger',    // 管理员
      8881: 'warning',  // 项目经理
      9528: 'info'      // 普通用户
    }
    return typeMap[roleId] || 'primary'
  }

  // 移除getRoleLabel函数，现在直接使用返回的roleName字段
  // const getRoleLabel = (roleId) => { ... }

  const openParameterDrawer = () => {
    parameterType.value = 'create'
    parameterFormData.value = {
      parameterName: '',
      roleId: null,
      roleIds: [], // 初始化为空数组
      parameterNameCn: '',
      assessmentCategoryId: selectID.value // 自动设置当前选中的考核类别ID
    }
    parameterDrawerVisible.value = true
  }

  const editParameter = (row) => {
    parameterType.value = 'update'

    // 处理角色数据转换
    let roleIds = []
    if (row.roleIds && Array.isArray(row.roleIds)) {
      // 如果已经是数组格式
      roleIds = row.roleIds
    } else if (row.roleId) {
      // 如果是单个角色ID，转换为数组
      if (typeof row.roleId === 'string' && row.roleId.includes(',')) {
        // 如果是逗号分隔的字符串，分割为数组
        roleIds = row.roleId.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
      } else {
        // 单个角色ID
        roleIds = [row.roleId]
      }
    }

    parameterFormData.value = {
      ...row,
      roleIds: roleIds,
      assessmentCategoryId: row.assessmentCategoryId || selectID.value
    }
    parameterDrawerVisible.value = true
  }

  const closeParameterDrawer = () => {
    parameterDrawerVisible.value = false
    parameterFormData.value = {
      parameterName: '',
      roleId: null,
      roleIds: [], // 重置为空数组
      parameterNameCn: '',
      assessmentCategoryId: null
    }
  }

  const parameterForm = ref(null)
  const submitParameter = () => {
    parameterForm.value.validate(async (valid) => {
      if (!valid) return

      try {
        // 准备提交数据，将多选角色转换为后端可接受的格式
        const submitData = { ...parameterFormData.value }

        // 将多选角色ID转换为逗号分隔的字符串（保持与后端兼容）
        if (submitData.roleIds && submitData.roleIds.length > 0) {
          submitData.roleId = submitData.roleIds.join(',')
        } else {
          submitData.roleId = null
        }

        // 移除前端专用字段
        delete submitData.roleIds

        let res
        if (parameterType.value === 'create') {
          res = await createCalculationParameters(submitData)
        } else {
          res = await updateCalculationParameters(submitData)
        }

        if (res.code === 0) {
          ElMessage.success('操作成功')
          closeParameterDrawer()
          getParameterListData() // 刷新参数列表
        } else {
          ElMessage.error(res.msg || '操作失败')
        }
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    })
  }

  const deleteParameter = (row) => {
    ElMessageBox.confirm('确定要删除这个参数吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const res = await deleteCalculationParameters({ id: row.id })
        if (res.code === 0) {
          ElMessage.success('删除成功')
          getParameterListData() // 刷新参数列表
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    })
  }

  const batchDeleteParameters = () => {
    ElMessageBox.confirm(`确定要删除选中的 ${selectedParameters.value.length} 个参数吗?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const selectedIds = selectedParameters.value.map(item => item.id)
        const res = await deleteCalculationParametersByIds({ ids: selectedIds })
        if (res.code === 0) {
          selectedParameters.value = []
          ElMessage.success('批量删除成功')
          getParameterListData() // 刷新参数列表
        } else {
          ElMessage.error(res.msg || '批量删除失败')
        }
      } catch (error) {
        console.error('批量删除失败:', error)
        ElMessage.error('批量删除失败')
      }
    })
  }

  // 计算方法相关数据
  const methodData = ref([])
  const allMethodData = ref([]) // 存储所有考核类别的计算方法数据
  const selectedMethods = ref([])
  const methodLoading = ref(false)

  // 可用参数列表
  const availableParameters = ref([])

  // 用户数据
  const allUsers = ref([])
  const userLoading = ref(false)

  // 组织相关数据
  const organizationTree = ref([])
  const organizationMap = ref(new Map())

  // 动态计算可用用户列表（排除已被其他计算方法分配的用户）
  const availableUsers = computed(() => {
    // 获取当前正在编辑的方法ID
    const currentMethodId = methodFormData.value.id

    // 获取所有其他计算方法已分配的用户ID（检查所有考核类别的计算方法）
    const assignedUserIds = new Set()

    allMethodData.value.forEach(method => {
      // 排除当前正在编辑的方法（新增时currentMethodId为undefined，所以会检查所有方法）
      if (method.id !== currentMethodId) {
        if (method.assignedUsers && Array.isArray(method.assignedUsers)) {
          method.assignedUsers.forEach(userId => {
            assignedUserIds.add(userId)
          })
        }
      }
    })

    // 返回用户列表，已被分配的用户设为禁用
    return allUsers.value.map(user => ({
      ...user,
      disabled: assignedUserIds.has(user.key)
    }))
  })

  const methodDrawerVisible = ref(false)
  const methodType = ref('')
  const methodFormData = ref({
    methodName: '',
    description: '',
    selectedParameters: [],
    assignedUsers: [],
    assessmentCategoryId: null,
    configMode: 'rule', // 只使用规则引擎模式
    ruleType: 'rule', // 只使用规则类型
    ruleVersion: '1.0', // 规则版本
    ruleConfig: { // 规则配置
      inputParameters: [],
      conditions: [],
      defaultValue: 0
    }
  })

  const methodRules = ref({
    methodName: [
      {
        required: true,
        message: '请输入计算方法名称',
        trigger: 'blur'
      }
    ],
    description: [
      {
        required: true,
        message: '请输入方法描述',
        trigger: 'blur'
      }
    ]
  })

  // 计算方法相关方法
  const handleMethodSelectionChange = (selection) => {
    selectedMethods.value = selection
  }

  // 更新可用参数列表
  const updateAvailableParameters = () => {
    availableParameters.value = parameterData.value
      .filter(param => methodFormData.value.selectedParameters.includes(param.id))
      .map(param => param.parameterName)
  }

  const openMethodDrawer = () => {
    methodType.value = 'create'
    methodFormData.value = {
      methodName: '',
      description: '',
      selectedParameters: [],
      assignedUsers: [],
      assessmentCategoryId: selectID.value, // 自动设置当前选中的考核类别ID
      configMode: 'rule',
      ruleType: 'rule',
      ruleVersion: '1.0',
      ruleConfig: {
        inputParameters: [],
        conditions: [],
        defaultValue: 0
      }
    }
    availableParameters.value = []
    methodDrawerVisible.value = true
  }

  const editMethod = async (row) => {
    methodType.value = 'update'

    try {
      // 从后端获取完整的计算方法数据
      const res = await findCalculationMethods({ id: row.id })
      if (res.code === 0) {
        const data = res.data

        // 初始化规则配置
        let ruleConfig = {
          inputParameters: [],
          conditions: [],
          defaultValue: 0
        }

        // 解析规则配置（从formula字段中解析JSON）
        if (data.formula) {
          try {
            const parsedConfig = JSON.parse(data.formula)
            if (parsedConfig.ruleType === 'rule_engine' || parsedConfig.inputParameters) {
              // 解析规则配置
              ruleConfig = {
                inputParameters: parsedConfig.inputParameters || [],
                conditions: parsedConfig.conditions || [],
                defaultValue: parsedConfig.defaultValue || 0
              }
            }
          } catch (error) {
            console.warn('解析规则配置失败，使用默认配置:', error)
            ElMessage.warning('规则配置解析失败，已使用默认配置')
          }
        }

        methodFormData.value = {
          ...data,
          selectedParameters: data.selectedParameters || [],
          assignedUsers: data.assignedUsers || [],
          assessmentCategoryId: data.assessmentCategoryId || selectID.value,
          configMode: 'rule',
          ruleType: 'rule',
          ruleVersion: data.ruleVersion || '1.0',
          ruleConfig: ruleConfig
        }

        updateAvailableParameters()
        methodDrawerVisible.value = true
      } else {
        ElMessage.error('获取计算方法详情失败')
      }
    } catch (error) {
      console.error('获取计算方法详情失败:', error)
      ElMessage.error('获取计算方法详情失败')
    }
  }

  const closeMethodDrawer = () => {
    methodDrawerVisible.value = false
    methodFormData.value = {
      methodName: '',
      description: '',
      selectedParameters: [],
      assignedUsers: [],
      assessmentCategoryId: null,
      configMode: 'rule',
      ruleType: 'rule',
      ruleVersion: '1.0',
      ruleConfig: {
        inputParameters: [],
        conditions: [],
        defaultValue: 0
      }
    }
    availableParameters.value = []
  }

  const methodForm = ref(null)
  const submitMethod = () => {
    methodForm.value.validate(async (valid) => {
      if (!valid) return

      try {
        // 准备提交数据
        const submitData = { ...methodFormData.value }

        // 规则引擎模式：将规则配置序列化为JSON存储在formula字段
        submitData.ruleType = 'rule'
        submitData.formula = JSON.stringify({
          ruleType: 'rule_engine',
          version: submitData.ruleVersion,
          metadata: {
            name: submitData.methodName,
            description: submitData.description
          },
          inputParameters: submitData.ruleConfig.inputParameters,
          conditions: submitData.ruleConfig.conditions,
          defaultValue: submitData.ruleConfig.defaultValue
        })

        let res
        if (methodType.value === 'create') {
          res = await createCalculationMethods(submitData)
        } else {
          res = await updateCalculationMethods(submitData)
        }

        if (res.code === 0) {
          ElMessage.success('操作成功')
          closeMethodDrawer()
          getMethodListData() // 刷新计算方法列表
        } else {
          ElMessage.error(res.msg || '操作失败')
        }
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    })
  }

  const deleteMethod = (row) => {
    ElMessageBox.confirm('确定要删除这个计算方法吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const res = await deleteCalculationMethods({ id: row.id })
        if (res.code === 0) {
          ElMessage.success('删除成功')
          getMethodListData() // 刷新计算方法列表
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    })
  }

  const batchDeleteMethods = () => {
    ElMessageBox.confirm(`确定要删除选中的 ${selectedMethods.value.length} 个计算方法吗?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const selectedIds = selectedMethods.value.map(item => item.id)
        const res = await deleteCalculationMethodsByIds({ ids: selectedIds })
        if (res.code === 0) {
          selectedMethods.value = []
          ElMessage.success('批量删除成功')
          getMethodListData() // 刷新计算方法列表
        } else {
          ElMessage.error(res.msg || '批量删除失败')
        }
      } catch (error) {
        console.error('批量删除失败:', error)
        ElMessage.error('批量删除失败')
      }
    })
  }



  // 更新默认值
  const updateDefaultValue = (value) => {
    methodFormData.value.ruleConfig.defaultValue = value
  }

  // 处理测试结果
  const handleTestResult = (result) => {
    console.log('规则测试结果:', result)
    if (result.success) {
      ElMessage.success(`测试成功，结果: ${result.result}`)
    } else {
      ElMessage.error(`测试失败: ${result.error}`)
    }
  }

  // 初始化数据
  const initData = async () => {
    await loadOrganizationTree()    // 加载组织树
    await getUserListData()         // 加载用户列表
    // 参数和方法列表会在考核类别加载完成后自动加载
  }

  initData()
</script>

<style lang="scss" scoped>
.active {
  background-color: var(--el-color-primary) !important;
  color: #fff;
}

.mode-description {
  margin-top: 4px;
}

.rule-engine-config {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;

  .el-form-item {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
