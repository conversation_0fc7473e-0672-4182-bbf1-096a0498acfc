# 规则引擎使用指南

## 功能概述

基于现有的计算方法管理界面，我们成功集成了强大的规则引擎功能，支持复杂的业务逻辑配置和聚合函数计算。

## 主要特性

### ✅ 已实现功能

1. **双模式支持**
   - 传统公式模式：保持原有的简单公式计算
   - 规则引擎模式：支持条件分支和复杂业务逻辑

2. **强大的表达式解析器**
   - 聚合函数：SUM(), AVG(), MAX(), MIN(), COUNT()
   - 数学函数：ROUND(), ABS(), SQRT(), POW(), CEIL(), FLOOR()
   - 条件函数：IF(), CASE()
   - 逻辑运算：&&, ||, !, ==, !=, >, <, >=, <=

3. **可视化配置界面**
   - 参数配置组件：支持多种数据类型
   - 条件构建器：可视化的条件表达式编辑
   - 公式编辑器：带提示的公式编辑功能
   - 实时测试面板：即时验证规则逻辑

## 使用方法

### 1. 创建规则引擎计算方法

1. 进入"考核管理" -> "计算方法"页面
2. 选择考核类别，点击"新建计算方法"
3. 在配置模式中选择"规则引擎"
4. 配置以下内容：

#### 输入参数配置
```
参数名称: projectScores
类型: array
必填: ✓
描述: 项目评分数组

参数名称: coefficient  
类型: number
必填: ✗
默认值: 1.0
描述: 调整系数
```

#### 规则条件配置
```
条件1: 多项目综合评分
触发条件: COUNT(projectScores) > 0 && COUNT(departmentScores) > 0
计算公式: ROUND(AVG(projectScores) * 0.4 + AVG(departmentScores) * 0.6, 2)
优先级: 1

条件2: 高分奖励
触发条件: MAX(projectScores) >= 95
计算公式: AVG(projectScores) * 1.2 * coefficient
优先级: 2
```

### 2. 测试规则配置

在规则测试面板中输入测试数据：

```json
{
  "projectScores": [85, 90, 88, 92],
  "departmentScores": [87, 89, 91],
  "coefficient": 1.2
}
```

点击"执行测试"查看计算结果和执行详情。

### 3. API调用示例

```javascript
// 前端调用规则计算
import { calculateByRule } from '@/api/assessment/calculationMethods'

const result = await calculateByRule({
  methodId: 3, // 规则引擎计算方法ID
  parameters: {
    projectScores: [85, 90, 88, 92, 87],
    departmentScores: [89, 91, 87],
    coefficient: 1.2
  }
})

console.log('计算结果:', result.data.result)
console.log('匹配条件:', result.data.appliedCondition)
console.log('执行时间:', result.data.executionTime + 'ms')
```

## 支持的函数详解

### 聚合函数

| 函数 | 语法 | 说明 | 示例 |
|------|------|------|------|
| SUM | `SUM(array)` | 求和 | `SUM(projectScores)` |
| AVG | `AVG(array)` | 平均值 | `AVG(projectScores)` |
| MAX | `MAX(array)` | 最大值 | `MAX(projectScores)` |
| MIN | `MIN(array)` | 最小值 | `MIN(projectScores)` |
| COUNT | `COUNT(array)` | 计数 | `COUNT(projectScores)` |

### 数学函数

| 函数 | 语法 | 说明 | 示例 |
|------|------|------|------|
| ROUND | `ROUND(number, decimals)` | 四舍五入 | `ROUND(87.666, 2)` |
| ABS | `ABS(number)` | 绝对值 | `ABS(-10)` |
| SQRT | `SQRT(number)` | 平方根 | `SQRT(16)` |
| POW | `POW(base, exponent)` | 幂运算 | `POW(2, 3)` |
| CEIL | `CEIL(number)` | 向上取整 | `CEIL(87.1)` |
| FLOOR | `FLOOR(number)` | 向下取整 | `FLOOR(87.9)` |

### 条件函数

| 函数 | 语法 | 说明 | 示例 |
|------|------|------|------|
| IF | `IF(condition, trueValue, falseValue)` | 条件判断 | `IF(score >= 90, score * 1.2, score)` |

## 实际应用场景

### 场景1: 多维度评分计算

```json
{
  "name": "综合绩效评分",
  "conditions": [
    {
      "when": "COUNT(projectScores) > 0 && COUNT(departmentScores) > 0 && COUNT(teamScores) > 0",
      "then": {
        "formula": "AVG(projectScores) * 0.5 + AVG(departmentScores) * 0.3 + AVG(teamScores) * 0.2"
      }
    }
  ]
}
```

### 场景2: 分级奖励计算

```json
{
  "name": "分级奖励评分",
  "conditions": [
    {
      "when": "AVG(scores) >= 95",
      "then": {
        "formula": "AVG(scores) * 1.5"
      }
    },
    {
      "when": "AVG(scores) >= 90",
      "then": {
        "formula": "AVG(scores) * 1.2"
      }
    },
    {
      "when": "AVG(scores) >= 80",
      "then": {
        "formula": "AVG(scores) * 1.0"
      }
    }
  ]
}
```

### 场景3: 复杂业务逻辑

```json
{
  "name": "复杂评分逻辑",
  "conditions": [
    {
      "when": "MAX(projectScores) >= 95 && MIN(projectScores) >= 80 && COUNT(projectScores) >= 3",
      "then": {
        "formula": "ROUND((SUM(projectScores) + MAX(projectScores) * 0.2) / COUNT(projectScores), 2)"
      }
    }
  ]
}
```

## 注意事项

1. **数据类型**：确保传入的数组参数格式正确
2. **空值处理**：系统会自动处理null值和空数组
3. **性能考虑**：复杂的嵌套函数可能影响计算性能
4. **测试验证**：建议在正式使用前充分测试规则逻辑
5. **版本管理**：规则变更会自动记录版本信息

## 故障排除

### 常见问题

1. **函数不识别**：检查函数名称是否正确，注意大小写
2. **参数类型错误**：确保传入的参数类型与函数要求匹配
3. **条件不匹配**：检查条件表达式的逻辑是否正确
4. **计算结果异常**：使用测试面板验证规则逻辑

### 调试技巧

1. 使用测试面板的执行详情查看每一步的计算过程
2. 从简单的条件开始，逐步增加复杂度
3. 检查参数名称是否与输入数据一致
4. 验证数组数据的格式和内容

## 技术支持

如遇到问题，请检查：
1. 浏览器控制台的错误信息
2. 后端日志中的详细错误描述
3. 规则配置的JSON格式是否正确
4. 测试数据是否符合参数要求

---

通过这个强大的规则引擎，您可以灵活配置各种复杂的业务逻辑，无需修改代码即可适应不断变化的业务需求！
