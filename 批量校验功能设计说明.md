# 批量校验功能设计说明

## 功能概述

将原有的单用户校验逻辑改进为支持批量校验和计算的接口，提高性能和用户体验。

## 设计目标

1. **保持向后兼容性** - 现有接口调用方式不变
2. **支持批量处理** - 一次请求可以校验多个用户
3. **提供详细信息** - 返回每个用户的详细校验结果
4. **灵活的错误处理** - 前端可以根据场景选择不同的处理方式

## 数据结构设计

### 1. ValidationDetail 校验详情

```go
type ValidationDetail struct {
    Type        string `json:"type"`        // 校验类型
    Status      string `json:"status"`      // 校验状态: "success", "error"
    Message     string `json:"message"`     // 具体的校验信息
    ProjectId   *int   `json:"projectId"`   // 项目ID（如果是项目相关的校验）
    ProjectName string `json:"projectName"` // 项目名称
    ManagerName string `json:"managerName"` // 项目负责人姓名
}
```

#### 校验类型 (Type)
- `coefficient_allocation` - 考核系数分配校验
- `project_manager_score` - 项目负责人评分校验
- `department_manager_score` - 机构负责人评分校验

#### 校验状态 (Status)
- `success` - 校验通过
- `error` - 校验失败

### 2. UserParameterScores 用户参数评分（扩展）

```go
type UserParameterScores struct {
    UserName          string                 `json:"userName"`
    UserNickName      string                 `json:"userNickName"`
    CalculationMethod *CalculationMethod     `json:"calculationMethod"`
    ParameterScores   []ParameterScoreDetail `json:"parameterScores"`
    UserSummary       ParameterScoreSummary  `json:"userSummary"`
    HasError          bool                   `json:"hasError"`
    ErrorMessage      string                 `json:"errorMessage"`
    
    // 新增校验相关字段
    ValidationStatus  string             `json:"validationStatus"`  // "success", "error"
    ValidationMessage string             `json:"validationMessage"` // 校验信息摘要
    ValidationDetails []ValidationDetail `json:"validationDetails"` // 详细的校验结果列表
}
```

## 处理流程

### 后端处理流程

1. **收集需要校验的用户**
   - 遍历所有用户的计算方法
   - 识别包含 `project_participation` 和 `project_manager_score` 参数的用户
   - 记录每个用户是否需要 `department_manager_score` 校验

2. **批量校验**
   - 对每个需要校验的用户调用校验函数
   - 收集所有校验结果，不因单个用户校验失败而中断

3. **组装返回结果**
   - 为每个用户设置校验状态和详细信息
   - 如果有校验错误，设置 `HasError` 和 `ErrorMessage` 字段

### 前端处理策略

#### 查看详情场景
```javascript
// 检查校验状态，如果有错误就显示
if (userResponse.validationStatus === "error") {
    ElMessage.error('获取员工详细信息失败: ' + userResponse.validationMessage)
    return
}
// 继续处理正常逻辑
```

#### 导出Excel场景
```javascript
// 收集所有有校验错误的用户
const usersWithErrors = users.filter(user => user.validationStatus === "error")
if (usersWithErrors.length > 0) {
    // 显示汇总的错误信息
    showBatchErrorMessage(usersWithErrors)
    return
}
// 继续导出流程
```

## 校验逻辑详解

### 1. 考核系数分配校验
- **检查内容**：用户在指定考核配置中是否有考核系数分配记录
- **错误情况**：
  - 查询失败：`查询考核系数分配失败: [错误详情]`
  - 无分配记录：`该名用户需要考核系数分配，请先进行分配`

### 2. 项目负责人评分校验
- **检查内容**：每个考核系数分配对应的项目是否有项目负责人评分
- **错误情况**：
  - 查询失败：`查询项目负责人评分失败: [错误详情]`
  - 获取项目信息失败：`获取项目信息失败: [错误详情]`
  - 缺少评分：`[项目名称]项目未评分-项目负责人[负责人姓名]`

### 3. 机构负责人评分校验
- **检查内容**：用户是否有机构负责人评分记录
- **错误情况**：
  - 查询失败：`查询机构负责人评分失败: [错误详情]`
  - 缺少评分：`机构负责人暂无评分`

## 性能优化

### 当前实现
- 批量收集需要校验的用户
- 减少重复的数据库查询
- 并行处理多个用户的校验

### 后续优化空间
1. **数据库查询优化**
   - 批量查询考核系数分配记录
   - 批量查询项目负责人评分记录
   - 批量查询机构负责人评分记录

2. **缓存机制**
   - 缓存项目信息和负责人信息
   - 缓存用户的计算方法信息

3. **索引优化**
   ```sql
   CREATE INDEX idx_aca_user_config ON assessment_coefficient_allocation(username, assessment_config_id);
   CREATE INDEX idx_pms_coefficient ON project_manager_score(coefficient_allocation_id);
   CREATE INDEX idx_dms_user_config ON department_manager_score(username, assessment_config_id);
   ```

## 错误信息示例

### 单个用户的校验结果
```json
{
  "userName": "zhangmeng",
  "validationStatus": "error",
  "validationMessage": "规模化"风光储氢+"综合能源系统半实物仿真测试平台开发项目未评分-项目负责人南雄",
  "validationDetails": [
    {
      "type": "project_manager_score",
      "status": "error",
      "message": "规模化"风光储氢+"综合能源系统半实物仿真测试平台开发项目未评分-项目负责人南雄",
      "projectId": 123,
      "projectName": "规模化"风光储氢+"综合能源系统半实物仿真测试平台开发",
      "managerName": "南雄"
    }
  ]
}
```

### 批量校验结果
```json
{
  "users": [
    {
      "userName": "zhangmeng",
      "validationStatus": "error",
      "validationMessage": "规模化"风光储氢+"综合能源系统半实物仿真测试平台开发项目未评分-项目负责人南雄"
    },
    {
      "userName": "caofan",
      "validationStatus": "error", 
      "validationMessage": "该名用户需要考核系数分配，请先进行分配"
    },
    {
      "userName": "lisi",
      "validationStatus": "success",
      "validationMessage": "校验通过"
    }
  ]
}
```

## 前端适配指南

### 1. 检查校验状态
```javascript
// 检查单个用户的校验状态
function checkUserValidation(user) {
    if (user.validationStatus === "error") {
        return {
            hasError: true,
            message: user.validationMessage,
            details: user.validationDetails
        }
    }
    return { hasError: false }
}
```

### 2. 批量错误处理
```javascript
// 收集所有校验错误
function collectValidationErrors(users) {
    const errors = []
    users.forEach(user => {
        if (user.validationStatus === "error") {
            errors.push({
                userName: user.userName,
                userNickName: user.userNickName,
                message: user.validationMessage,
                details: user.validationDetails
            })
        }
    })
    return errors
}
```

### 3. 错误信息展示
```javascript
// 显示详细的校验错误信息
function showValidationErrors(errors) {
    let message = '<div style="text-align: left;">'
    message += '<p><strong>以下用户存在校验问题：</strong></p>'
    
    errors.forEach((error, index) => {
        message += `<p style="margin: 5px 0;"><strong>${index + 1}. ${error.userNickName}</strong></p>`
        message += `<p style="margin: 5px 0 10px 20px; color: #666;">${error.message}</p>`
    })
    
    message += '</div>'
    
    ElMessageBox.alert(message, '校验失败', {
        confirmButtonText: '确定',
        type: 'warning',
        dangerouslyUseHTMLString: true
    })
}
```

## 测试场景

### 1. 单用户查看详情
- **正常用户**：显示详情页面
- **缺少考核系数分配**：显示"该名用户需要考核系数分配，请先进行分配"
- **缺少项目负责人评分**：显示"[项目名]项目未评分-项目负责人[姓名]"
- **缺少机构负责人评分**：显示"机构负责人暂无评分"

### 2. 批量导出Excel
- **所有用户正常**：正常导出Excel
- **部分用户有问题**：显示汇总错误信息，不导出
- **混合场景**：显示所有有问题的用户列表

### 3. 性能测试
- **小批量**：10个用户的校验和计算
- **中批量**：100个用户的校验和计算
- **大批量**：1000个用户的校验和计算

## 相关文件

### 后端文件
- `server/model/assessment/request/assessment_data.go` - 数据结构定义
- `server/service/assessment/assessment_data.go` - 业务逻辑实现
- `server/api/v1/assessment/assessment_data.go` - API接口

### 前端文件
- `web/src/view/score/projectAssessmentScore/projectAssessmentScore.vue` - 主要页面
- `web/src/api/assessment.js` - API调用

### 文档文件
- `用户参数评分校验功能说明.md` - 原有校验功能说明
- `数据库字段错误修复说明.md` - 数据库修复说明
- `双重错误提示问题分析与修复.md` - 错误处理修复说明
