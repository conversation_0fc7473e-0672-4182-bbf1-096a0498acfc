# 考核系数数据加载到表格功能

## 问题分析

### 当前状况
- 后端返回了考核系数数据，包含用户名、项目ID和系数值
- 前端表格显示了有 `project_participation` 参数的成员
- 但表格中的系数值都是0，没有加载已存在的数据

### 数据结构分析

**后端返回的数据**：
```json
{
  "coefficientData": {
    "hasCurrentData": true,
    "currentAssessmentId": 12,
    "coefficients": [
      {
        "id": 11,
        "assessmentConfigId": 12,
        "username": "10159180",
        "projectId": 69,
        "assessmentCoefficient": 100,
        "calculationParameter": "project_participation"
      },
      {
        "id": 12,
        "assessmentConfigId": 12,
        "username": "10159179",
        "projectId": 21,
        "assessmentCoefficient": 100,
        "calculationParameter": "project_participation"
      }
    ]
  }
}
```

**前端表格的数据结构**：
```javascript
tabsData[tabName].scores = {
  "69-38": 100,  // 项目ID-用户ID: 系数值
  "21-39": 100   // 项目ID-用户ID: 系数值
}
```

## 解决方案

### 1. 修改数据填充逻辑

在 `populateTabsData` 函数中添加考核系数数据加载：

```javascript
// 初始化评分数据为0
projects.forEach(project => {
  members.forEach(member => {
    const key = `${project.id}-${member.id}`
    tabsData[tab.name].scores[key] = 0
  })
})

// 加载已存在的考核系数数据
loadExistingCoefficientData(tab, data)
```

### 2. 新增数据加载函数

创建 `loadExistingCoefficientData` 函数：

```javascript
const loadExistingCoefficientData = (tab, data) => {
  // 获取当前标签页对应的考核配置ID
  const configId = tab.configId
  
  // 查找对应考核配置的系数数据
  let coefficientsToLoad = []
  
  if (data.coefficientData) {
    // 如果当前考核配置有数据，使用当前数据
    if (data.coefficientData.hasCurrentData && 
        data.coefficientData.currentAssessmentId === configId) {
      coefficientsToLoad = data.coefficientData.coefficients || []
    }
    // 如果当前考核配置没有数据，但有历史数据且匹配
    else if (!data.coefficientData.hasCurrentData && 
             data.coefficientData.previousAssessmentId === configId) {
      coefficientsToLoad = data.coefficientData.previousCoefficients || []
    }
  }
  
  // 将考核系数数据加载到表格中
  coefficientsToLoad.forEach(coeff => {
    const key = `${coeff.projectId}-${getUserIdByUsername(coeff.username, data)}`
    if (tabsData[tab.name] && tabsData[tab.name].scores) {
      tabsData[tab.name].scores[key] = coeff.assessmentCoefficient
    }
  })
}
```

### 3. 用户名到用户ID的映射

创建 `getUserIdByUsername` 函数：

```javascript
const getUserIdByUsername = (username, data) => {
  if (data.adminData && data.adminData.orgMembers) {
    const member = data.adminData.orgMembers.find(m => m.userName === username)
    return member ? member.userId : null
  }
  return null
}
```

## 数据流程

### 1. 数据匹配逻辑

```javascript
// 标签页配置ID = 12 ("7月考核任务")
tab.configId = 12

// 后端数据
coefficientData.currentAssessmentId = 12
coefficientData.hasCurrentData = true
coefficientData.coefficients = [...]

// 匹配成功，加载当前数据
```

### 2. 数据转换过程

```javascript
// 后端数据
{
  username: "10159180",
  projectId: 69,
  assessmentCoefficient: 100
}

// 转换为表格key
username "10159180" → userId 38 (通过 getUserIdByUsername)
key = "69-38"

// 加载到表格
tabsData["assessment_12"].scores["69-38"] = 100
```

### 3. 表格显示效果

| 成员 | 项目69 | 项目21 |
|------|--------|--------|
| 齐春凯(10159180) | 100 | 0 |
| 刘博伟(10159179) | 0 | 100 |

## 成员筛选逻辑

### 当前逻辑保持不变

```javascript
// 只有计算参数中包含project_participation的用户才显示在表格中
if (hasProjectParticipationParameter(member)) {
  members.push({
    id: member.userId,
    name: member.nickName,
    userName: member.userName,
    // ...
  })
}
```

### 筛选条件

用户必须满足以下条件才会显示在表格中：
1. 属于当前部门
2. 计算方法中包含 `project_participation` 参数
3. 用户状态有效（未删除）

## 测试验证

### 1. 数据加载测试

1. 刷新页面
2. 查看控制台日志：
   ```
   开始为标签页 assessment_12 加载考核系数数据
   标签页 assessment_12 使用当前考核数据: [...]
   加载系数数据: 10159180 在项目 69 的系数为 100
   加载系数数据: 10159179 在项目 21 的系数为 100
   标签页 assessment_12 加载了 2 条考核系数数据
   ```

### 2. 表格显示测试

1. 切换到"7月考核任务"标签页
2. 验证表格中显示的系数值：
   - 齐春凯在项目69的系数应该显示100
   - 刘博伟在项目21的系数应该显示100
   - 其他位置应该显示0

### 3. 多标签页测试

1. 切换到"2025月度考核"标签页
2. 验证是否加载了对应的历史数据
3. 确认不同标签页的数据独立显示

## 预期效果

修改后，用户将看到：
- ✅ 表格中正确显示已存在的考核系数数据
- ✅ 不同标签页显示对应考核配置的数据
- ✅ 只显示有 `project_participation` 参数的成员
- ✅ 数据与后端返回的考核系数一致

这样用户就可以看到之前提交的考核系数数据，并可以进行修改和更新。
