# 考核系数数据加载修复

## 问题分析

### 当前状况
- 后端正确返回了所有考核配置的数据（包含 `allCoefficients` 字段）
- 考核配置12的标签页能正确显示数据
- 考核配置11的标签页无法显示数据

### 问题根因

原来的 `loadExistingCoefficientData` 函数逻辑有缺陷：

```javascript
// 问题逻辑
if (data.coefficientData.hasCurrentData && 
    data.coefficientData.currentAssessmentId === configId) {
  // 只有当前考核配置ID匹配时才加载数据
}
else if (!data.coefficientData.hasCurrentData && 
         data.coefficientData.previousAssessmentId === configId) {
  // 只有当没有当前数据且历史配置ID匹配时才加载数据
}
```

**问题**：
- 当前数据：`hasCurrentData = true`, `currentAssessmentId = 12`
- 对于考核配置11的标签页：
  - 第一个条件：`12 !== 11` ❌
  - 第二个条件：`hasCurrentData = true` (不是false) ❌
- 结果：考核配置11的数据无法加载

## 修复方案

### 使用 `allCoefficients` 字段

修改逻辑，优先使用后端返回的 `allCoefficients` 字段：

```javascript
// 修复后的逻辑
if (data.coefficientData.allCoefficients && data.coefficientData.allCoefficients.length > 0) {
  // 从所有数据中筛选出当前标签页对应考核配置的数据
  coefficientsToLoad = data.coefficientData.allCoefficients.filter(coeff => 
    coeff.assessmentConfigId === configId
  )
}
```

### 数据筛选逻辑

```javascript
// 对于考核配置12的标签页 (configId = 12)
allCoefficients.filter(coeff => coeff.assessmentConfigId === 12)
// 返回: [
//   {assessmentConfigId: 12, username: "10159179", projectId: 21, assessmentCoefficient: 100},
//   {assessmentConfigId: 12, username: "10159180", projectId: 69, assessmentCoefficient: 100}
// ]

// 对于考核配置11的标签页 (configId = 11)
allCoefficients.filter(coeff => coeff.assessmentConfigId === 11)
// 返回: [
//   {assessmentConfigId: 11, username: "10159180", projectId: 21, assessmentCoefficient: 31},
//   {assessmentConfigId: 11, username: "10159180", projectId: 69, assessmentCoefficient: 69}
// ]
```

## 数据流程

### 1. 后端返回数据
```json
{
  "allCoefficients": [
    {"assessmentConfigId": 12, "username": "10159179", "projectId": 21, "assessmentCoefficient": 100},
    {"assessmentConfigId": 12, "username": "10159180", "projectId": 69, "assessmentCoefficient": 100},
    {"assessmentConfigId": 11, "username": "10159180", "projectId": 21, "assessmentCoefficient": 31},
    {"assessmentConfigId": 11, "username": "10159180", "projectId": 69, "assessmentCoefficient": 69}
  ]
}
```

### 2. 标签页数据筛选

**"7月考核任务"标签页** (configId = 12)：
```javascript
coefficientsToLoad = allCoefficients.filter(coeff => coeff.assessmentConfigId === 12)
// 结果: 2条记录
```

**"2025月度考核"标签页** (configId = 11)：
```javascript
coefficientsToLoad = allCoefficients.filter(coeff => coeff.assessmentConfigId === 11)
// 结果: 2条记录
```

### 3. 表格数据加载

**考核配置12的数据**：
- `10159179` 在项目21：100%
- `10159180` 在项目69：100%

**考核配置11的数据**：
- `10159180` 在项目21：31%
- `10159180` 在项目69：69%

## 调试信息

修复后的控制台日志应该显示：

```
开始为标签页 assessment_12 加载考核系数数据
标签页 assessment_12 (配置ID: 12) 从 allCoefficients 中筛选出数据: [...]
加载系数数据: 10159179 在项目 21 的系数为 100
加载系数数据: 10159180 在项目 69 的系数为 100
标签页 assessment_12 加载了 2 条考核系数数据

开始为标签页 assessment_11 加载考核系数数据
标签页 assessment_11 (配置ID: 11) 从 allCoefficients 中筛选出数据: [...]
加载系数数据: 10159180 在项目 21 的系数为 31
加载系数数据: 10159180 在项目 69 的系数为 69
标签页 assessment_11 加载了 2 条考核系数数据
```

## 兜底逻辑

为了保持向后兼容性，保留了原有的逻辑作为兜底：

```javascript
// 如果没有 allCoefficients，使用原有逻辑作为兜底
else {
  if (data.coefficientData.hasCurrentData && 
      data.coefficientData.currentAssessmentId === configId) {
    coefficientsToLoad = data.coefficientData.coefficients || []
  }
  else if (!data.coefficientData.hasCurrentData && 
           data.coefficientData.previousAssessmentId === configId) {
    coefficientsToLoad = data.coefficientData.previousCoefficients || []
  }
}
```

## 测试步骤

1. **刷新前端页面**
2. **查看控制台日志**，确认两个标签页都有加载日志
3. **切换到"2025月度考核"标签页**
4. **验证表格显示**：
   - 齐春凯在项目21：31%
   - 齐春凯在项目69：69%
5. **切换到"7月考核任务"标签页**
6. **验证表格显示**：
   - 刘博伟在项目21：100%
   - 齐春凯在项目69：100%

## 预期结果

修复后，每个标签页都应该能正确显示对应考核配置的数据：
- ✅ 利用 `allCoefficients` 字段获取完整数据
- ✅ 按 `assessmentConfigId` 正确筛选数据
- ✅ 支持多个考核配置的独立显示
- ✅ 保持向后兼容性
