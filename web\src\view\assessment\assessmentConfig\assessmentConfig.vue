
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
            <el-form-item label="考核配置名称" prop="assessmentName">
  <el-input v-model="searchInfo.assessmentName" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="考核类型" prop="assessmentType">
  <el-input v-model="searchInfo.assessmentType" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="考核周期" prop="assessmentPeriod">
  <el-input v-model="searchInfo.assessmentPeriod" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="是否归档" prop="isArchived">
  <el-select v-model="searchInfo.isArchived" clearable placeholder="请选择">
    <el-option key="true" label="是" value="true"></el-option>
    <el-option key="false" label="否" value="false"></el-option>
  </el-select>
</el-form-item>
            

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        >
        <el-table-column type="selection" width="55" />
        
            <el-table-column align="left" label="考核配置名称" prop="assessmentName" width="120" />

            <el-table-column align="left" label="考核类型" prop="assessmentType" width="120" />

            <el-table-column align="left" label="考核周期" prop="assessmentPeriod" width="120" />

            <el-table-column align="left" label="是否归档" prop="isArchived" width="120">
    <template #default="scope">{{ formatBoolean(scope.row.isArchived) }}</template>
</el-table-column>
            <el-table-column align="left" label="算法关联" prop="algorithmName" width="150" />

            <el-table-column align="left" label="奖金关联" prop="bonusName" width="150" />

            <el-table-column align="left" label="高分配额关联" prop="quotaName" width="150" />

            <el-table-column align="left" label="创建时间" prop="createdAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.createdAt) }}</template>
</el-table-column>
            <el-table-column align="left" label="更新时间" prop="updatedAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.updatedAt) }}</template>
</el-table-column>
        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateAssessmentConfigFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="考核配置名称:" prop="assessmentName">
    <el-input v-model="formData.assessmentName" :clearable="true" placeholder="请输入考核配置名称" />
</el-form-item>
            <el-form-item label="考核类型:" prop="assessmentType">
              <el-radio-group v-model="formData.assessmentType">
                <el-radio value="月度考核">月度考核</el-radio>
                <el-radio value="季度考核">季度考核</el-radio>
                <el-radio value="年度考核">年度考核</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="考核周期:" prop="assessmentPeriod">
              <!-- 月度考核：年-月选择器 -->
              <el-date-picker
                v-if="formData.assessmentType === '月度考核'"
                v-model="formData.assessmentPeriod"
                type="month"
                placeholder="选择年月"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
              <!-- 季度考核：下拉选择框 -->
              <el-select
                v-else-if="formData.assessmentType === '季度考核'"
                v-model="formData.assessmentPeriod"
                placeholder="请选择季度"
                style="width: 100%"
              >
                <el-option
                  v-for="item in quarterOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <!-- 年度考核：年份输入框 -->
              <el-input
                v-else-if="formData.assessmentType === '年度考核'"
                v-model="formData.assessmentPeriod"
                placeholder="请输入考核年份 如2025"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="算法关联:" prop="algorithmRelationId">
              <el-select
                v-model="formData.algorithmRelationId"
                placeholder="请选择考核类别"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in assessmentCategories"
                  :key="item.id"
                  :label="item.categoryName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="奖金关联:" prop="bonusRelationId">
              <el-select
                v-model="formData.bonusRelationId"
                placeholder="请选择奖金"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in bonusManagement"
                  :key="item.id"
                  :label="item.bonusName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="高分配额关联:" prop="scoreQuotaId">
              <el-select
                v-model="formData.scoreQuotaId"
                placeholder="请选择配额"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in scoreQuotaManagement"
                  :key="item.id"
                  :label="item.quotaName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>

          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="考核配置名称">
    {{ detailFrom.assessmentName }}
</el-descriptions-item>
                    <el-descriptions-item label="考核类型">
    {{ detailFrom.assessmentType }}
</el-descriptions-item>
                    <el-descriptions-item label="考核周期">
    {{ detailFrom.assessmentPeriod }}
</el-descriptions-item>
                    <el-descriptions-item label="是否归档">
    {{ detailFrom.isArchived }}
</el-descriptions-item>
                    <el-descriptions-item label="算法关联">
    {{ detailFrom.algorithmName || '未设置' }}
</el-descriptions-item>
                    <el-descriptions-item label="奖金关联">
    {{ detailFrom.bonusName || '未设置' }}
</el-descriptions-item>
                    <el-descriptions-item label="高分配额关联">
    {{ detailFrom.quotaName || '未设置' }}
</el-descriptions-item>
            </el-descriptions>
        </el-drawer>

  </div>
</template>

<script setup>
import {
  createAssessmentConfig,
  deleteAssessmentConfig,
  deleteAssessmentConfigByIds,
  updateAssessmentConfig,
  findAssessmentConfig,
  findAssessmentConfigWithNames,
  getAssessmentConfigList,
  getAssessmentConfigWithNamesList
} from '@/api/assessment/assessmentConfig'

// 导入相关API
import { getAssessmentCategoriesList } from '@/api/assessment/assessmentCategories'
import { getBonusManagementList } from '@/api/assessment/bonusManagement'
import { getScoreQuotaManagementList } from '@/api/assessment/scoreQuotaManagement'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, watch } from 'vue'
import { useAppStore } from "@/pinia"




defineOptions({
    name: 'AssessmentConfig'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            assessmentName: '',
            assessmentType: '月度考核', // 默认选择月度考核
            assessmentPeriod: '',
            algorithmRelationId: undefined,
            bonusRelationId: undefined,
            scoreQuotaId: undefined
        })

// 考核类型选项
const assessmentTypeOptions = [
  { label: '月度考核', value: '月度考核' },
  { label: '季度考核', value: '季度考核' },
  { label: '年度考核', value: '年度考核' }
]

// 季度选项
const quarterOptions = [
  { label: '第一季度', value: '第一季度' },
  { label: '第二季度', value: '第二季度' },
  { label: '第三季度', value: '第三季度' },
  { label: '第四季度', value: '第四季度' }
]

// 下拉框数据源
const assessmentCategories = ref([]) // 考核类别（算法关联）
const bonusManagement = ref([])      // 奖金管理
const scoreQuotaManagement = ref([]) // 高分配额管理



// 验证规则
const rule = reactive({
               assessmentName : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
               assessmentType : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
               assessmentPeriod : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
               algorithmRelationId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
               bonusRelationId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
               scoreQuotaId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    if (searchInfo.value.isArchived === ""){
        searchInfo.value.isArchived=null
    }
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getAssessmentConfigWithNamesList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteAssessmentConfigFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          ids.push(item.id)
        })
      const res = await deleteAssessmentConfigByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateAssessmentConfigFunc = async(row) => {
    const res = await findAssessmentConfig({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteAssessmentConfigFunc = async (row) => {
    const res = await deleteAssessmentConfig({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        assessmentName: '',
        assessmentType: '月度考核', // 默认选择月度考核
        assessmentPeriod: '',
        algorithmRelationId: undefined,
        bonusRelationId: undefined,
        scoreQuotaId: undefined
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createAssessmentConfig(formData.value)
                  break
                case 'update':
                  res = await updateAssessmentConfig(formData.value)
                  break
                default:
                  res = await createAssessmentConfig(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findAssessmentConfigWithNames({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}

// 加载考核类别数据（算法关联）
const loadAssessmentCategories = async () => {
  try {
    const res = await getAssessmentCategoriesList({
      page: 1,
      pageSize: 999
    })
    if (res.code === 0) {
      assessmentCategories.value = res.data.list || []
    }
  } catch (error) {
    console.error('加载考核类别失败:', error)
  }
}

// 加载奖金管理数据
const loadBonusManagement = async () => {
  try {
    const res = await getBonusManagementList({
      page: 1,
      pageSize: 999
    })
    if (res.code === 0) {
      bonusManagement.value = res.data.list || []
    }
  } catch (error) {
    console.error('加载奖金管理数据失败:', error)
    ElMessage.error('加载奖金管理数据失败')
  }
}

// 加载高分配额管理数据
const loadScoreQuotaManagement = async () => {
  try {
    const res = await getScoreQuotaManagementList({
      page: 1,
      pageSize: 999
    })
    if (res.code === 0) {
      scoreQuotaManagement.value = res.data.list || []
    }
  } catch (error) {
    console.error('加载高分配额管理数据失败:', error)
    ElMessage.error('加载高分配额管理数据失败')
  }
}

// 初始化加载所有下拉框数据
const initDropdownData = async () => {
  await Promise.all([
    loadAssessmentCategories(),
    loadBonusManagement(),
    loadScoreQuotaManagement()
  ])
}

// 监听考核类型变化，清空考核周期
watch(() => formData.value.assessmentType, (newType, oldType) => {
  if (newType !== oldType) {
    formData.value.assessmentPeriod = ''
  }
})

// 页面初始化
initDropdownData()


</script>

<style>

</style>
