# 考核系数查询逻辑修改说明

## 修改概述

已将后端的考核系数查询逻辑从"只返回当前用户的数据"修改为"返回当前部门所有成员的数据"。

## 修改的方法

### 1. `getCoefficientData` 方法

**修改前**：
```go
func (s *AssessmentDataService) getCoefficientData(ctx context.Context, userName string) (assessmentReq.CoefficientData, error)
```
- 只查询当前登录用户的考核系数数据
- 查询条件：`WHERE aca.username = ?`

**修改后**：
```go
func (s *AssessmentDataService) getCoefficientData(ctx context.Context, userName string, orgId int) (assessmentReq.CoefficientData, error)
```
- 查询当前部门所有成员的考核系数数据
- 查询条件：`WHERE aca.username IN (部门所有用户名)`

### 2. `getPreviousCoefficients` 方法

**修改前**：
```go
func (s *AssessmentDataService) getPreviousCoefficients(ctx context.Context, userName string, currentAssessmentId int) ([]assessmentReq.CoefficientInfo, int, error)
```

**修改后**：
```go
func (s *AssessmentDataService) getPreviousCoefficients(ctx context.Context, userName string, currentAssessmentId int, orgId int) ([]assessmentReq.CoefficientInfo, int, error)
```

## 查询逻辑

### 1. 获取部门用户列表
```sql
SELECT u.username
FROM org_organizational_user ou
LEFT JOIN sys_users u ON ou.user_id = u.id
WHERE ou.org_id = ? AND u.deleted_at IS NULL
```

### 2. 查询部门所有成员的系数数据
```sql
SELECT aca.id, aca.assessment_config_id, aca.username, aca.project_id, 
       aca.assessment_coefficient, pms.manager_score, pms.scorer_username, 
       aca.calculation_parameter, aca.created_at
FROM assessment_coefficient_allocation aca
LEFT JOIN project_manager_score pms ON aca.id = pms.coefficient_allocation_id
WHERE aca.assessment_config_id = ? AND aca.username IN (部门用户列表)
```

## 测试验证

### 测试数据
- **考核配置ID**: 11 (2025月度考核)
- **部门ID**: 5 (数字能源智联技术研究所)
- **部门成员**: 12个用户，包括 10159180、10159179 等

### 查询结果
```json
[
  {
    "id": 5,
    "assessment_config_id": 11,
    "username": "10159180",
    "project_id": 23,
    "assessment_coefficient": 100,
    "manager_score": null,
    "scorer_username": null,
    "calculation_parameter": "project_participation",
    "created_at": "2025-07-22T10:27:53.002Z"
  },
  {
    "id": 6,
    "assessment_config_id": 11,
    "username": "10159179",
    "project_id": 21,
    "assessment_coefficient": 100,
    "manager_score": null,
    "scorer_username": null,
    "calculation_parameter": "project_participation",
    "created_at": "2025-07-22T10:27:53.002Z"
  }
]
```

## 前端影响

### 修改前的返回数据
```json
{
  "hasCurrentData": false,
  "currentAssessmentId": 12,
  "coefficients": null,
  "previousAssessmentId": 11,
  "previousCoefficients": null
}
```

### 修改后的预期返回数据
```json
{
  "hasCurrentData": true,
  "currentAssessmentId": 11,
  "coefficients": [
    {
      "id": 5,
      "assessmentConfigId": 11,
      "username": "10159180",
      "projectId": 23,
      "assessmentCoefficient": 100,
      "calculationParameter": "project_participation"
    },
    {
      "id": 6,
      "assessmentConfigId": 11,
      "username": "10159179",
      "projectId": 21,
      "assessmentCoefficient": 100,
      "calculationParameter": "project_participation"
    }
  ]
}
```

## 业务逻辑改进

1. **权限范围扩大**: 从个人数据扩展到部门数据
2. **数据完整性**: 显示部门内所有成员的考核系数分配情况
3. **管理便利性**: 管理员可以查看整个部门的考核系数分配状态
4. **数据一致性**: 确保部门内考核数据的统一管理

## 注意事项

1. **权限控制**: 用户只能查看自己所在部门的数据
2. **性能考虑**: 增加了部门用户查询，但通过索引优化性能
3. **数据安全**: 通过 `deleted_at IS NULL` 确保只查询有效用户
4. **向后兼容**: 保持返回数据结构不变，只是数据范围扩大

## 下一步测试

1. 重启后端服务
2. 刷新前端页面
3. 验证是否能看到部门所有成员的考核系数数据
4. 确认数据显示正确且完整
