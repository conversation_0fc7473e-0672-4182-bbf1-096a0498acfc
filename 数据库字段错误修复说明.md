# 数据库字段错误修复说明

## 问题描述

在实现用户参数评分校验功能时，出现了以下错误：

```
获取用户参数评分失败: 获取项目信息失败: 项目不存在: Error 1054 (42S22): Unknown column 'manager_username' in 'field list'
```

## 问题原因

在 `getProjectInfoAndManager` 函数中，错误地使用了不存在的数据库字段：

### 错误的字段名称
- `manager_username` ❌
- `leader_username` ❌  
- `manager` ❌

### 实际的数据库结构
根据 `project_info` 表的实际结构：

```sql
CREATE TABLE `project_info` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `name` varchar(100) NOT NULL COMMENT '项目名称',
  `department_id` mediumint NULL DEFAULT NULL COMMENT '所属部门ID',
  `manager_id` varchar(50) NULL DEFAULT NULL COMMENT '项目经理用户名', -- ✅ 正确字段
  `type` varchar(20) NOT NULL DEFAULT '自研项目' COMMENT '项目类型',
  `members` json NULL COMMENT '项目成员',
  `created_at` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NULL DEFAULT NULL COMMENT '更新时间'
);
```

## 修复方案

### 修复前的代码
```go
func (s *AssessmentDataService) getProjectInfoAndManager(ctx context.Context, projectId int) (string, string, error) {
    type ProjectInfo struct {
        Name            string  `json:"name"`
        ManagerUsername *string `json:"managerUsername"`  // ❌ 错误字段
        LeaderUsername  *string `json:"leaderUsername"`   // ❌ 错误字段
        Manager         *string `json:"manager"`          // ❌ 错误字段
    }

    var project ProjectInfo
    err := global.GVA_DB.Table("project_info").
        Select("name, manager_username, leader_username, manager"). // ❌ 错误查询
        Where("id = ?", projectId).
        First(&project).Error
    // ...
}
```

### 修复后的代码
```go
func (s *AssessmentDataService) getProjectInfoAndManager(ctx context.Context, projectId int) (string, string, error) {
    type ProjectInfo struct {
        Name      string  `json:"name"`
        ManagerId *string `json:"managerId"`  // ✅ 正确字段
    }

    var project ProjectInfo
    err := global.GVA_DB.Table("project_info").
        Select("name, manager_id").  // ✅ 正确查询
        Where("id = ?", projectId).
        First(&project).Error
    
    if err != nil {
        return "", "", fmt.Errorf("项目不存在: %v", err)
    }

    projectName := project.Name
    if projectName == "" {
        projectName = fmt.Sprintf("项目%d", projectId)
    }

    // 获取项目负责人姓名
    var managerName string
    if project.ManagerId != nil && *project.ManagerId != "" {
        // 从sys_users表获取用户昵称
        var user struct {
            NickName string `json:"nickName"`
        }
        err = global.GVA_DB.Table("sys_users").
            Select("nick_name").
            Where("username = ?", *project.ManagerId).
            First(&user).Error
        if err == nil && user.NickName != "" {
            managerName = user.NickName
        } else {
            // 如果找不到用户昵称，使用用户名
            managerName = *project.ManagerId
        }
    } else {
        managerName = "未知"
    }

    return projectName, managerName, nil
}
```

## 修复要点

### 1. 字段名称修正
- 使用正确的字段名 `manager_id` 而不是 `manager_username`
- 移除不存在的字段 `leader_username` 和 `manager`

### 2. 项目负责人获取逻辑
- 从 `project_info.manager_id` 获取项目负责人的用户名
- 通过用户名从 `sys_users.nick_name` 获取显示名称
- 如果找不到昵称，则使用用户名作为显示名称
- 如果 `manager_id` 为空，则显示 "未知"

### 3. 错误处理改进
- 保持原有的错误处理逻辑
- 确保在各种情况下都能返回合适的项目负责人名称

## 测试验证

修复后，以下操作应该能正常工作：

1. **查看详情功能** - 点击员工评分的"查看详情"按钮
2. **导出Excel功能** - 点击"导出Excel"按钮
3. **校验逻辑** - 当用户缺少项目负责人评分时，能正确显示项目名称和负责人信息

### 预期的错误信息格式
```json
{
  "code": 500,
  "msg": "获取用户参数评分失败: 测试项目未评分-项目负责人张三"
}
```

## 相关文件

### 修改的文件
- `server/service/assessment/assessment_data.go` - 修复 `getProjectInfoAndManager` 函数

### 相关的数据库表
- `project_info` - 项目信息表
- `sys_users` - 用户信息表
- `assessment_coefficient_allocation` - 考核系数分配表
- `project_manager_score` - 项目负责人评分表

## 注意事项

1. **数据库字段一致性** - 确保代码中使用的字段名与实际数据库表结构一致
2. **用户昵称获取** - 优先使用用户昵称，如果获取失败则使用用户名
3. **空值处理** - 正确处理 `manager_id` 为空的情况
4. **错误信息** - 提供清晰的错误信息，便于用户理解和处理

## 后续建议

1. **代码审查** - 检查其他地方是否也存在类似的字段名错误
2. **数据库文档** - 维护准确的数据库表结构文档
3. **单元测试** - 添加针对 `getProjectInfoAndManager` 函数的单元测试
4. **集成测试** - 测试完整的校验流程，确保各种场景都能正常工作
