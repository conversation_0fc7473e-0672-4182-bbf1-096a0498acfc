package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type CalculationParametersRouter struct{}

// InitCalculationParametersRouter 初始化 计算参数表 路由信息
func (s *CalculationParametersRouter) InitCalculationParametersRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	calculationParametersRouter := Router.Group("calculationParameters").Use(middleware.OperationRecord())
	calculationParametersRouterWithoutRecord := Router.Group("calculationParameters")
	calculationParametersRouterWithoutAuth := PublicRouter.Group("calculationParameters")
	{
		calculationParametersRouter.POST("createCalculationParameters", calculationParametersApi.CreateCalculationParameters)             // 新建计算参数表
		calculationParametersRouter.DELETE("deleteCalculationParameters", calculationParametersApi.DeleteCalculationParameters)           // 删除计算参数表
		calculationParametersRouter.DELETE("deleteCalculationParametersByIds", calculationParametersApi.DeleteCalculationParametersByIds) // 批量删除计算参数表
		calculationParametersRouter.PUT("updateCalculationParameters", calculationParametersApi.UpdateCalculationParameters)              // 更新计算参数表
	}
	{
		calculationParametersRouterWithoutRecord.GET("findCalculationParameters", calculationParametersApi.FindCalculationParameters)                               // 根据ID获取计算参数表
		calculationParametersRouterWithoutRecord.GET("getCalculationParametersList", calculationParametersApi.GetCalculationParametersList)                         // 获取计算参数表列表
		calculationParametersRouterWithoutRecord.GET("getCalculationParametersWithRoleNameList", calculationParametersApi.GetCalculationParametersWithRoleNameList) // 获取包含角色名称的计算参数表列表
	}
	{
		calculationParametersRouterWithoutAuth.GET("getCalculationParametersPublic", calculationParametersApi.GetCalculationParametersPublic) // 计算参数表开放接口
	}
}
