# 项目负责人校验优化说明

## 优化背景

在原有的校验逻辑中，系统会检查所有考核系数分配对应的项目是否有项目负责人评分。但是存在一个合理的场景：**如果项目负责人就是被分配考核系数的用户本人，那么该用户不需要给自己评分**。

## 问题场景

### 原有逻辑问题
```
用户：张三
考核系数分配：张三被分配到"项目A"的考核系数
项目A的负责人：张三

原有校验结果：❌ "项目A项目未评分-项目负责人张三"
```

这种情况下，要求张三给自己评分是不合理的。

### 优化后的逻辑
```
用户：张三
考核系数分配：张三被分配到"项目A"的考核系数
项目A的负责人：张三

优化后校验结果：✅ 跳过校验（项目负责人是自己，无需评分）
```

## 技术实现

### 1. 函数重构

#### 新增函数：`getProjectInfoAndManagerWithId`
```go
func (s *AssessmentDataService) getProjectInfoAndManagerWithId(ctx context.Context, projectId int) (string, string, string, error) {
    // 返回：项目名称, 负责人姓名, 负责人用户名, 错误
}
```

#### 修改函数：`getProjectInfoAndManager`
```go
func (s *AssessmentDataService) getProjectInfoAndManager(ctx context.Context, projectId int) (string, string, error) {
    // 调用新函数，保持向后兼容
    projectName, managerName, _, err := s.getProjectInfoAndManagerWithId(ctx, projectId)
    return projectName, managerName, err
}
```

### 2. 校验逻辑优化

#### 原有逻辑
```go
if scoreCount == 0 {
    // 获取项目信息
    projectName, managerName, err := s.getProjectInfoAndManager(ctx, *coeff.ProjectId)
    
    // 直接报告未评分错误
    validationDetails = append(validationDetails, ValidationDetail{
        Type:    "project_manager_score",
        Status:  "error",
        Message: fmt.Sprintf("%s项目未评分-项目负责人%s", projectName, managerName),
    })
}
```

#### 优化后逻辑
```go
if scoreCount == 0 {
    // 获取项目信息（包含负责人ID）
    projectName, managerName, managerId, err := s.getProjectInfoAndManagerWithId(ctx, *coeff.ProjectId)
    
    // 检查项目负责人是否与被分配系数的用户是同一人
    if managerId != "" && managerId == userName {
        // 项目负责人是自己，跳过校验
        continue
    }
    
    // 不是同一人，报告未评分错误
    validationDetails = append(validationDetails, ValidationDetail{
        Type:    "project_manager_score",
        Status:  "error", 
        Message: fmt.Sprintf("%s项目未评分-项目负责人%s", projectName, managerName),
    })
}
```

### 3. 数据库查询优化

#### 项目信息查询
```go
type ProjectInfo struct {
    Name      string  `json:"name"`
    ManagerId *string `json:"managerId"`
}

// 查询项目基本信息
err := global.GVA_DB.Table("project_info").
    Select("name, manager_id").
    Where("id = ?", projectId).
    First(&project).Error
```

#### 负责人信息处理
```go
var managerName string
var managerId string

if project.ManagerId != nil && *project.ManagerId != "" {
    managerId = *project.ManagerId
    
    // 查询负责人昵称
    var user struct {
        NickName string `json:"nickName"`
    }
    err = global.GVA_DB.Table("sys_users").
        Select("nick_name").
        Where("username = ?", *project.ManagerId).
        First(&user).Error
        
    if err == nil && user.NickName != "" {
        managerName = user.NickName
    } else {
        managerName = *project.ManagerId
    }
} else {
    managerName = "未知"
    managerId = ""
}
```

## 校验场景分析

### 场景1：项目负责人是自己
```
输入：
- userName: "zhangsan"
- 项目ID: 123
- 项目负责人: "zhangsan"

处理：
- 检测到 managerId == userName
- 跳过校验（continue）
- 不产生校验错误

结果：✅ 校验通过
```

### 场景2：项目负责人是其他人，且已评分
```
输入：
- userName: "zhangsan" 
- 项目ID: 123
- 项目负责人: "lisi"
- 评分记录: 存在

处理：
- scoreCount > 0
- 不进入未评分检查逻辑

结果：✅ 校验通过
```

### 场景3：项目负责人是其他人，但未评分
```
输入：
- userName: "zhangsan"
- 项目ID: 123  
- 项目负责人: "lisi"
- 评分记录: 不存在

处理：
- scoreCount == 0
- managerId != userName
- 产生校验错误

结果：❌ "项目A项目未评分-项目负责人李四"
```

### 场景4：项目没有负责人
```
输入：
- userName: "zhangsan"
- 项目ID: 123
- 项目负责人: null 或 ""
- 评分记录: 不存在

处理：
- scoreCount == 0
- managerId == ""，不等于 userName
- 产生校验错误

结果：❌ "项目A项目未评分-项目负责人未知"
```

## 向后兼容性

### 1. 函数兼容
- 保留原有的 `getProjectInfoAndManager` 函数
- 新增 `getProjectInfoAndManagerWithId` 函数
- 原有调用代码无需修改

### 2. 数据结构兼容
- 校验结果的数据结构保持不变
- 只是减少了不必要的校验错误
- 前端处理逻辑无需修改

### 3. 业务逻辑兼容
- 合理的校验逻辑优化
- 不影响其他校验规则
- 提高用户体验

## 测试用例

### 测试用例1：自己是项目负责人
```
用户：张三
项目：AI系统开发项目
项目负责人：张三
期望结果：跳过项目负责人评分校验
```

### 测试用例2：他人是项目负责人且已评分
```
用户：张三
项目：AI系统开发项目  
项目负责人：李四
评分状态：李四已评分
期望结果：校验通过
```

### 测试用例3：他人是项目负责人但未评分
```
用户：张三
项目：AI系统开发项目
项目负责人：李四
评分状态：李四未评分
期望结果：校验失败，提示"AI系统开发项目项目未评分-项目负责人李四"
```

### 测试用例4：项目无负责人
```
用户：张三
项目：AI系统开发项目
项目负责人：未设置
评分状态：无评分
期望结果：校验失败，提示"AI系统开发项目项目未评分-项目负责人未知"
```

## 优化效果

### 1. 业务逻辑更合理
- 避免要求用户给自己评分的不合理情况
- 符合实际业务场景需求
- 提高系统的智能化程度

### 2. 用户体验提升
- 减少不必要的错误提示
- 降低用户困惑
- 提高操作效率

### 3. 系统稳定性
- 保持向后兼容
- 不影响现有功能
- 代码结构清晰

## 部署注意事项

### 1. 数据库依赖
- 确保 `project_info` 表的 `manager_id` 字段数据准确
- 确保 `sys_users` 表的用户名和昵称数据完整

### 2. 测试验证
- 测试各种项目负责人场景
- 验证批量校验功能正常
- 确认前端显示正确

### 3. 监控观察
- 观察校验错误数量的变化
- 监控用户反馈
- 记录优化效果

## 总结

本次优化通过增加项目负责人身份检查，避免了用户给自己评分的不合理校验要求，使系统的校验逻辑更加智能和人性化。同时保持了良好的向后兼容性，不会影响现有功能的正常运行。
