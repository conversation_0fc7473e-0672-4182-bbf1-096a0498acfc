import service from '@/utils/request'

// @Tags CalculationParameters
// @Summary 创建计算参数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.CalculationParameters true "创建计算参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /calculationParameters/createCalculationParameters [post]
export const createCalculationParameters = (data) => {
  return service({
    url: '/calculationParameters/createCalculationParameters',
    method: 'post',
    data
  })
}

// @Tags CalculationParameters
// @Summary 删除计算参数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.CalculationParameters true "删除计算参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /calculationParameters/deleteCalculationParameters [delete]
export const deleteCalculationParameters = (params) => {
  return service({
    url: '/calculationParameters/deleteCalculationParameters',
    method: 'delete',
    params
  })
}

// @Tags CalculationParameters
// @Summary 批量删除计算参数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除计算参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /calculationParameters/deleteCalculationParametersByIds [delete]
export const deleteCalculationParametersByIds = (params) => {
  return service({
    url: '/calculationParameters/deleteCalculationParametersByIds',
    method: 'delete',
    params
  })
}

// @Tags CalculationParameters
// @Summary 更新计算参数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.CalculationParameters true "更新计算参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /calculationParameters/updateCalculationParameters [put]
export const updateCalculationParameters = (data) => {
  return service({
    url: '/calculationParameters/updateCalculationParameters',
    method: 'put',
    data
  })
}

// @Tags CalculationParameters
// @Summary 用id查询计算参数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.CalculationParameters true "用id查询计算参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /calculationParameters/findCalculationParameters [get]
export const findCalculationParameters = (params) => {
  return service({
    url: '/calculationParameters/findCalculationParameters',
    method: 'get',
    params
  })
}

// @Tags CalculationParameters
// @Summary 分页获取计算参数列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取计算参数列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /calculationParameters/getCalculationParametersList [get]
export const getCalculationParametersList = (params) => {
  return service({
    url: '/calculationParameters/getCalculationParametersList',
    method: 'get',
    params
  })
}

// @Tags CalculationParameters
// @Summary 分页获取包含角色名称的计算参数列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取计算参数列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /calculationParameters/getCalculationParametersWithRoleNameList [get]
export const getCalculationParametersWithRoleNameList = (params) => {
  return service({
    url: '/calculationParameters/getCalculationParametersWithRoleNameList',
    method: 'get',
    params
  })
}
